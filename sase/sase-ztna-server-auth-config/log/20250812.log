{"time":"2025-08-12T19:10:52.9732998+08:00","level":"INFO","msg":"[初始化] - Redis: {false 0 [***************:36379]  0}"}
{"time":"2025-08-12T19:10:52.9917967+08:00","level":"INFO","msg":"[初始化] - MongoDB: {false mongodb.outside.rongma.tech:27017 alpha-rmclient zo3RdOgGio*F alpha-rm_sase  false}"}
{"time":"2025-08-12T19:12:47.1071684+08:00","level":"INFO","msg":"[初始化] - Redis: {Switch:false ConnectionMode:0 ResourcePrefix: Address:[***************:36379] Password: DB:0}"}
{"time":"2025-08-12T19:12:47.1233617+08:00","level":"INFO","msg":"[初始化] - MongoDB: {Switch:false ResourcePrefix: Address: DBName: Username: Password: Direct:false ReplicaSet: Options:}"}
{"time":"2025-08-12T19:13:53.579879+08:00","level":"INFO","msg":"[初始化] - Redis: {Switch:false ConnectionMode:0 ResourcePrefix: Address:[***************:36379] Password: DB:0}"}
{"time":"2025-08-12T19:13:53.5990199+08:00","level":"INFO","msg":"[初始化] - MongoDB: {Switch:false ResourcePrefix: Address: DBName: Username: Password: Direct:false ReplicaSet: Options:}"}
{"time":"2025-08-12T19:15:23.8653579+08:00","level":"INFO","msg":"[初始化] - Redis: {Switch:false ConnectionMode:0 ResourcePrefix: Address:[***************:36379] Password: DB:0}"}
{"time":"2025-08-12T19:15:23.8874487+08:00","level":"INFO","msg":"[初始化] - MongoDB: {Switch:false ResourcePrefix: Address: DBName: Username: Password: Direct:false ReplicaSet: Options:}"}
{"time":"2025-08-12T19:15:57.9440792+08:00","level":"INFO","msg":"[初始化] - Redis: {Switch:false ConnectionMode:0 ResourcePrefix: Address:[***************:36379] Password: DB:0}"}
{"time":"2025-08-12T19:15:57.9617452+08:00","level":"INFO","msg":"[初始化] - MongoDB: {Switch:false ResourcePrefix: Address: DBName: Username: Password: Direct:false ReplicaSet: Options:}"}
{"time":"2025-08-12T19:18:48.6499635+08:00","level":"INFO","msg":"[初始化] - Redis: {Switch:false ConnectionMode:0 ResourcePrefix: Address:[***************:36379] Password: DB:0}"}
{"time":"2025-08-12T19:18:48.671201+08:00","level":"INFO","msg":"[初始化] - MongoDB: {Switch:false ResourcePrefix:alpha- Address:mongodb.outside.rongma.tech:27017 DBName: Username:alpha-rmclient Password:zo3RdOgGio*F Direct:true ReplicaSet: Options:}"}
{"time":"2025-08-12T19:20:23.7636486+08:00","level":"INFO","msg":"[初始化] - Redis: {Switch:false ConnectionMode:0 ResourcePrefix: Address:[***************:36379] Password: DB:0}"}
{"time":"2025-08-12T19:20:23.7841244+08:00","level":"INFO","msg":"[初始化] - MongoDB: {Switch:false ResourcePrefix:alpha- Address:mongodb.outside.rongma.tech:27017 DBName:rm_sase Username:alpha-rmclient Password:zo3RdOgGio*F Direct:true ReplicaSet: Options:}"}
{"time":"2025-08-12T19:22:03.3252104+08:00","level":"INFO","msg":"[初始化] - Redis: {Switch:false ConnectionMode:0 ResourcePrefix: Address:[***************:36379] Password: DB:0}"}
{"time":"2025-08-12T19:22:03.3437279+08:00","level":"INFO","msg":"[初始化] - MongoDB: {Switch:false ResourcePrefix:alpha- Address:mongodb.outside.rongma.tech:27017 DBName:rm_sase Username:alpha-rmclient Password:zo3RdOgGio*F Direct:true ReplicaSet: Options:}"}
{"time":"2025-08-12T19:35:05.6085139+08:00","level":"INFO","msg":"[初始化] - Redis: {Switch:false ConnectionMode:0 ResourcePrefix: Address:[***************:36379] Password: DB:0}"}
{"time":"2025-08-12T19:35:05.6295323+08:00","level":"INFO","msg":"[初始化] - MongoDB: {Switch:false ResourcePrefix:alpha- Address:mongodb.outside.rongma.tech:27017 DBName:rm_sase Username:alpha-rmclient Password:zo3RdOgGio*F Direct:true ReplicaSet: Options:}"}
{"time":"2025-08-12T19:39:15.8262763+08:00","level":"INFO","msg":"[初始化] - Redis: {Switch:false ConnectionMode:0 ResourcePrefix: Address:[***************:36379] Password: DB:0}"}
{"time":"2025-08-12T19:39:15.8437827+08:00","level":"INFO","msg":"[初始化] - MongoDB: {Switch:false ResourcePrefix:alpha- Address:mongodb.outside.rongma.tech:27017 DBName:rm_sase Username:alpha-rmclient Password:zo3RdOgGio*F Direct:true ReplicaSet: Options:}"}
{"time":"2025-08-12T19:39:55.3794967+08:00","level":"INFO","msg":"[初始化] - Redis: {Switch:false ConnectionMode:0 ResourcePrefix: Address:[***************:36379] Password: DB:0}"}
{"time":"2025-08-12T19:39:55.3958784+08:00","level":"INFO","msg":"[初始化] - MongoDB: {Switch:false ResourcePrefix:alpha- Address:mongodb.outside.rongma.tech:27017 DBName:rm_sase Username:alpha-rmclient Password:zo3RdOgGio*F Direct:true ReplicaSet: Options:}"}
{"time":"2025-08-12T19:40:44.0718333+08:00","level":"INFO","msg":"[初始化] - Redis: {Switch:false ConnectionMode:0 ResourcePrefix: Address:[***************:36379] Password: DB:0}"}
{"time":"2025-08-12T19:40:44.0915526+08:00","level":"INFO","msg":"[初始化] - MongoDB: {Switch:false ResourcePrefix:alpha- Address:mongodb.outside.rongma.tech:27017 DBName:rm_sase Username:alpha-rmclient Password:zo3RdOgGio*F Direct:true ReplicaSet: Options:}"}
{"time":"2025-08-12T19:41:12.4964129+08:00","level":"INFO","msg":"[初始化] - Redis: {Switch:false ConnectionMode:0 ResourcePrefix: Address:[***************:36379] Password: DB:0}"}
{"time":"2025-08-12T19:41:12.5146039+08:00","level":"INFO","msg":"[初始化] - MongoDB: {Switch:false ResourcePrefix:alpha- Address:mongodb.outside.rongma.tech:27017 DBName:rm_sase Username:alpha-rmclient Password:zo3RdOgGio*F Direct:true ReplicaSet: Options:}"}
{"time":"2025-08-12T19:42:08.4431107+08:00","level":"INFO","msg":"[初始化] - Redis: {Switch:false ConnectionMode:0 ResourcePrefix: Address:[***************:36379] Password: DB:0}"}
{"time":"2025-08-12T19:42:08.4625217+08:00","level":"INFO","msg":"[初始化] - MongoDB: {Switch:false ResourcePrefix:alpha- Address:mongodb.outside.rongma.tech:27017 DBName:rm_sase Username:alpha-rmclient Password:zo3RdOgGio*F Direct:true ReplicaSet: Options:}"}
{"time":"2025-08-12T19:42:11.1730894+08:00","level":"ERROR","msg":"LdapAccountLogin 域信息不存在, DomainName: Roomasec.com                 , LdapDN: DC=roomasec,DC=com"}
{"time":"2025-08-12T19:42:11.1751223+08:00","level":"ERROR","msg":"根据用户account chenyang 和code  获取用户信息失败: mongo: no documents in result , param: &{ rmalpha chenyang true Roomasec.com                  }"}
{"time":"2025-08-12T19:43:10.5677385+08:00","level":"INFO","msg":"[初始化] - Redis: {Switch:false ConnectionMode:0 ResourcePrefix: Address:[***************:36379] Password: DB:0}"}
{"time":"2025-08-12T19:43:10.5842154+08:00","level":"INFO","msg":"[初始化] - MongoDB: {Switch:false ResourcePrefix:alpha- Address:mongodb.outside.rongma.tech:27017 DBName:rm_sase Username:alpha-rmclient Password:zo3RdOgGio*F Direct:true ReplicaSet: Options:}"}
{"time":"2025-08-12T19:43:12.559+08:00","level":"ERROR","msg":"LdapAccountLogin 域信息不存在, DomainName: Roomasec.com                 , LdapDN: DC=roomasec,DC=com"}
{"time":"2025-08-12T19:45:23.2064847+08:00","level":"ERROR","msg":"LdapAccountLogin 域信息不存在, DomainName: rm.com                 , LdapDN: DC=rm,DC=com"}
{"time":"2025-08-12T19:45:37.8900486+08:00","level":"ERROR","msg":"LdapAccountLogin 域信息不存在, DomainName: Roomasec.com                 , LdapDN: DC=roomasec,DC=com"}
{"time":"2025-08-12T19:45:46.5672659+08:00","level":"ERROR","msg":"LdapAccountLogin 域信息不存在, DomainName: rm.com                 , LdapDN: DC=rm,DC=com"}
{"time":"2025-08-12T19:46:02.1098335+08:00","level":"ERROR","msg":"LdapAccountLogin 域信息不存在, DomainName: Roomasec.com                 , LdapDN: DC=roomasec,DC=com"}
{"time":"2025-08-12T19:49:18.1361649+08:00","level":"INFO","msg":"[初始化] - Redis: {Switch:false ConnectionMode:0 ResourcePrefix: Address:[***************:36379] Password: DB:0}"}
{"time":"2025-08-12T19:49:18.1593953+08:00","level":"INFO","msg":"[初始化] - MongoDB: {Switch:false ResourcePrefix:alpha- Address:mongodb.outside.rongma.tech:27017 DBName:rm_sase Username:alpha-rmclient Password:zo3RdOgGio*F Direct:true ReplicaSet: Options:}"}
{"time":"2025-08-12T19:49:48.1632322+08:00","level":"ERROR","msg":"LdapAccountLogin 域信息不存在, DomainName: Roomasec.com                 "}
{"time":"2025-08-12T19:50:41.3013654+08:00","level":"ERROR","msg":"LdapAccountLogin 域信息不存在, DomainName: Roomasec.com                 "}
{"time":"2025-08-12T19:55:52.3039111+08:00","level":"INFO","msg":"[初始化] - Redis: {Switch:false ConnectionMode:0 ResourcePrefix: Address:[***************:36379] Password: DB:0}"}
{"time":"2025-08-12T19:55:52.3263145+08:00","level":"INFO","msg":"[初始化] - MongoDB: {Switch:false ResourcePrefix:alpha- Address:mongodb.outside.rongma.tech:27017 DBName:rm_sase Username:alpha-rmclient Password:zo3RdOgGio*F Direct:true ReplicaSet: Options:}"}
{"time":"2025-08-12T19:56:45.3079645+08:00","level":"ERROR","msg":"LdapAccountLogin 域信息不存在, DomainName:        roomasec          "}
{"time":"2025-08-12T19:56:53.8130706+08:00","level":"ERROR","msg":"LdapAccountLogin 域信息不存在, DomainName: roomasec"}
{"time":"2025-08-12T20:22:33.6428384+08:00","level":"ERROR","msg":"LdapAccountLogin 域信息不存在, DomainName: rm.com        "}
{"time":"2025-08-12T20:23:30.176419+08:00","level":"INFO","msg":"[初始化] - Redis: {Switch:false ConnectionMode:0 ResourcePrefix: Address:[***************:36379] Password: DB:0}"}
{"time":"2025-08-12T20:23:30.1925278+08:00","level":"INFO","msg":"[初始化] - MongoDB: {Switch:false ResourcePrefix:alpha- Address:mongodb.outside.rongma.tech:27017 DBName:rm_sase Username:alpha-rmclient Password:zo3RdOgGio*F Direct:true ReplicaSet: Options:}"}
{"time":"2025-08-12T20:23:37.942386+08:00","level":"ERROR","msg":"根据用户account chenyang 和code  获取用户信息失败: mongo: no documents in result , param: &{ rmalpha chenyang false rm.com         }"}
{"time":"2025-08-12T20:24:41.9700444+08:00","level":"INFO","msg":"[初始化] - Redis: {Switch:false ConnectionMode:0 ResourcePrefix: Address:[***************:36379] Password: DB:0}"}
{"time":"2025-08-12T20:24:41.9873549+08:00","level":"INFO","msg":"[初始化] - MongoDB: {Switch:false ResourcePrefix:alpha- Address:mongodb.outside.rongma.tech:27017 DBName:rm_sase Username:alpha-rmclient Password:zo3RdOgGio*F Direct:true ReplicaSet: Options:}"}
