[service]
name = 'ztna_server_auth'
mode = 'dev'
version = '1.0.0.0'
addr = "0.0.0.0:8080"
is_test_mode = true

[logging]
type = 'text'
dir = 'log'
level = 'info'
stdout = true

[mongodb_rm_sase]
direct = true
resource_prefix = 'alpha-'
addr = 'mongodb.outside.rongma.tech:27017'
dbname = "rm_sase"
username = "alpha-rmclient"
password = "zo3RdOgGio*F"

[redis_default]
# 0-单机模式 1-集群模式
connection_mode = 0
addr = ['192.168.111.185:36379']
password = ""
db = 0

[jwt]
secret_key = "your-secure-jwt-secret-key-should-be-very-long"
access_expiry = 7200 # 2小时（秒）
refresh_expiry = 2592000 # 30天（秒）
issuer = "ztna_auth_service"

[sms]
secret_id = 'AKIDqaCcqpaBYfYAZjfGvPjrkPr6B0Zv53nK'
secret_key = 'NVSMEqCI8h9QYWFjxwf7KG84qPkgMnzu'
sms_sdk_appid = '1400789505'
captcha_appid = 193511179
captcha_app_secret_key = 'MhZw9KSblVv6JVtDubE0ImQGE'
sign_name = '戎码科技'
# 同一个手机号验证码两次发送时间间隔（秒）
verification_code_interval = 120
# 每个手机号验证码有效时间（分钟）
verification_code_valid_time = 5
# 【不替换生产的】同一个手机号验证码每天允许的最大发送次数，0点更新
verification_code_times_day_for_phone = 30
# 【不替换生产的】同一个IP验证码每天允许的最大发送次数，0点更新
verification_code_times_day_for_ip = 100

[third_party]
address = "http://sase-org-sync-service.sase-app-service:80"