2025-08-12 14:48:04.887 INFO [main.go:37[main]] 服务正在启动...
2025-08-12 14:48:04.892 INFO [resource.go:36[Init]] 初始化 jwt 成功
2025-08-12 14:48:04.931 INFO [common.go:22[Init]] 初始化Model层公共实例...
2025-08-12 14:48:04.931 INFO [office_setting.go:72[NewRepository]] 使用MongoDB作为办公设置仓库
2025-08-12 14:48:04.931 INFO [common.go:28[Init]] Model层公共实例初始化完成
2025-08-12 14:48:04.931 INFO [routes.go:23[RegisterRoutes]] 开始注册API路由
2025-08-12 14:48:04.934 INFO [routes.go:667[RegisterRoutes]] API路由注册完成
2025-08-12 14:48:04.934 INFO [main.go:68[main]] 服务器已启动，监听端口: 8080, 模式: debug
2025-08-12 14:48:04.934 INFO [main.go:62[func1]] HTTP服务器监听端口: 8080
2025-08-12 14:48:04.936 FATAL [main.go:64[func1]] HTTP服务器启动失败: listen tcp :8080: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.
