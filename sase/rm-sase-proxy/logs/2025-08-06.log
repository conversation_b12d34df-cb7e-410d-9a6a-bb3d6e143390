2025-08-06 16:01:42.443 INFO [main.go:37[main]] 服务正在启动...
2025-08-06 16:01:42.447 INFO [resource.go:41[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-08-06 16:01:42.447 INFO [resource.go:47[Init]] 初始化 jwt 成功
2025-08-06 16:05:19.094 INFO [main.go:37[main]] 服务正在启动...
2025-08-06 16:05:19.098 INFO [resource.go:41[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-08-06 16:05:19.098 INFO [resource.go:47[Init]] 初始化 jwt 成功
2025-08-06 16:06:53.205 INFO [main.go:37[main]] 服务正在启动...
2025-08-06 16:06:53.208 INFO [resource.go:40[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-08-06 16:06:53.208 INFO [resource.go:46[Init]] 初始化 jwt 成功
2025-08-06 16:06:53.210 INFO [common.go:22[Init]] 初始化Model层公共实例...
2025-08-06 16:06:53.210 INFO [office_setting.go:72[NewRepository]] 使用MongoDB作为办公设置仓库
2025-08-06 16:06:53.210 INFO [common.go:28[Init]] Model层公共实例初始化完成
2025-08-06 16:06:53.210 INFO [routes.go:23[RegisterRoutes]] 开始注册API路由
2025-08-06 16:06:53.213 INFO [routes.go:667[RegisterRoutes]] API路由注册完成
2025-08-06 16:06:53.213 INFO [main.go:68[main]] 服务器已启动，监听端口: 8080, 模式: debug
2025-08-06 16:06:53.213 INFO [main.go:62[func1]] HTTP服务器监听端口: 8080
2025-08-06 16:07:05.976 INFO [main.go:74[main]] 正在关闭服务器...
2025-08-06 16:07:05.976 INFO [main.go:85[main]] 服务器已关闭
2025-08-06 16:07:10.493 INFO [main.go:37[main]] 服务正在启动...
2025-08-06 16:07:10.496 INFO [resource.go:41[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-08-06 16:07:10.497 INFO [resource.go:47[Init]] 初始化 jwt 成功
2025-08-06 16:10:07.910 INFO [main.go:37[main]] 服务正在启动...
2025-08-06 16:10:07.914 INFO [resource.go:41[Init]] Redis连接成功，地址: [192.168.111.185:36379], 数据库: 0
2025-08-06 16:10:07.914 INFO [resource.go:47[Init]] 初始化 jwt 成功
2025-08-06 16:10:07.936 INFO [common.go:22[Init]] 初始化Model层公共实例...
2025-08-06 16:10:07.936 INFO [office_setting.go:72[NewRepository]] 使用MongoDB作为办公设置仓库
2025-08-06 16:10:07.936 INFO [common.go:28[Init]] Model层公共实例初始化完成
2025-08-06 16:10:07.936 INFO [routes.go:23[RegisterRoutes]] 开始注册API路由
2025-08-06 16:10:07.938 INFO [routes.go:667[RegisterRoutes]] API路由注册完成
2025-08-06 16:10:07.938 INFO [main.go:68[main]] 服务器已启动，监听端口: 8080, 模式: debug
2025-08-06 16:10:07.938 INFO [main.go:62[func1]] HTTP服务器监听端口: 8080
2025-08-06 16:11:45.648 INFO [main.go:74[main]] 正在关闭服务器...
2025-08-06 16:11:45.648 INFO [main.go:85[main]] 服务器已关闭
