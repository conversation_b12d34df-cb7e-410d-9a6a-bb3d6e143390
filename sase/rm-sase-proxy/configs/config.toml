[server]
port = 8080
export_limit = 1000
# debug, release, test
mode = "debug"
access_control_allow_origin = ["*"]
sase_context = "/sase"
sase_cloud_api = "https://alpha-sase-app.rongma.tech"
logout_jump_url = "https://www-alpha.rongma.tech"

[redis]
addr = "192.168.111.185:36379"
password = ""
db = 0

[jwt]
prefix = "ratpconsolejwt"
sign_key = "ratpconsolejwt"
expired_time = "24h"

[passport]
appid = 47
app_secret = "f1160b82e8836b682dcfd117888eb6fe"
callback = "https://alpha-sase-test.rongma.tech"
passport_web_url = "https://alpha-passport.rongma.tech"
passport_backend_url = "http://passport-api.passport.svc.cluster.local/rm_passport/v1"
console_web_callback_url = "main/login"

[log]
level = "info"
path = "./logs" 

[verify_code]
sms_secret_id = 'AKIDqaCcqpaBYfYAZjfGvPjrkPr6B0Zv53nK'
sms_secret_key = 'NVSMEqCI8h9QYWFjxwf7KG84qPkgMnzu'
sms_sdk_appid = '1400789505'
sms_sign_name = '戎码科技'
sms_tpl_id = '2345805'
# console 用户需要重新验证的频率
default_interval = "24h"
# 同一个手机号验证码两次发送时间间隔（秒）
verification_code_interval = 120
# 每个手机号验证码有效时间（分钟）
verification_code_valid_time = 5
# 【不替换生产的】同一个手机号验证码每天允许的最大发送次数，0点更新
verification_code_times_day = 30
# 【不替换生产的】同一个IP验证码每天允许的最大发送次数，0点更新
verification_code_times_day_for_ip = 1000

[email]
port = 465
host = "smtpdm.aliyun.com"
user = "<EMAIL>"
password = "Gz5SPWbsatH3"
alias_name = "X-WING NG-EDR System"

[actlogger]
debug = true
actlog_index_name = "alpha-sase-proxy-log"

[elasticsearch_ext]
proxy_index_name =  "alpha-console-proxy-log"

[elasticsearch_default]
resource_prefix = 'alpha-'
addr = ['http://elasticsearch.outside.rongma.tech:9200']
username = 'elastic'
password = 'elastic'
pipeline = ''

[mongodb]
addr = 'mongodb.outside.rongma.tech:27017'
database = "alpha-rm_sase"
username = "alpha-rmclient"
password = "zo3RdOgGio*F"