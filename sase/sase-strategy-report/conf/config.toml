[service]
# 处理消息的协程数量 必须大于0
kafka_goroutine_num = 5
data_goroutine_num = 5
kafka_goroutine_internal = 200
attribute_data_addr = "http://192.168.111.185:44852"
attribute_data_cache_time = 100 # 分钟

[kafka_consumer]
topic = "sase-log-tracker"
group_id = "sase-log-tracker-reader"

[mongodb_sase_report]
switch = true
resource_prefix = "alpha-"
addr = 'mongodb.outside.rongma.tech:27017'
username = 'alpha-rmclient'
password = 'zo3RdOgGio*F'
dbname = "sase_report"

[mongodb_rm_sase]
switch = true
resource_prefix = "alpha-"
addr = 'mongodb.outside.rongma.tech:27017'
username = 'alpha-rmclient'
password = 'zo3RdOgGio*F'
dbname = "rm_sase"

[redis_default]
switch = true
# connection_mode 连接方式，0 为单机模式，1 为集群模式
connection_mode = 0
addr = ['192.168.111.185:36379']
password = ''
db = 0

[kafka_default]
# 资源前缀，topic 前缀，比如 alpha-preduce-event，那么配 alpha-
resource_prefix = 'alpha-'
addr = [
    'kafka0.outside.rongma.tech:19092',
    'kafka1.outside.rongma.tech:29092',
    'kafka2.outside.rongma.tech:39092',
]
username = ''
password = ''