[server]
port = 8686
mode = "debug" # debug, release, test
max_export_limit = 1000

[mongodb]
addr = 'mongodb.outside.rongma.tech:27017'
database = "alpha-rm_sase"
username = "alpha-rmclient"
password = "zo3RdOgGio*F"

[mongodb_client_instructions]
addr = 'mongodb.outside.rongma.tech:27017'
database = 'alpha-client_instructions'
username = 'alpha-rmclient'
password = 'zo3RdOgGio*F'

[mongodb_sase_report]
addr = 'mongodb.outside.rongma.tech:27017'
database = 'alpha-sase_report'
username = 'alpha-rmclient'
password = 'zo3RdOgGio*F'

[mongodb_engine]
addr = 'mongodb.outside.rongma.tech:27017'
database = 'alpha-engines'
username = 'alpha-rmclient'
password = 'zo3RdOgGio*F'

[grpc_client_strategy]
is_cheack_cert = false
addr = 'strategy-grpc.client.svc.cluster.local:6060'

[grpc_group]
# 是否验证证书
is_cheack_cert = false
addr = 'rm-group.client.svc.cluster.local:6060'

[redis]
# 0-单机模式 1-集群模式
connection_mode = 0
addr = "192.168.111.185:36379"
password = ""
db = 0

[certs]
rsa_2048 = 'certs/rsa2048.prvkey'
rsa_4096 = 'certs/rsa4096.prvkey'
crt = "certs/client.crt"
key = "certs/client.key"
ca = "certs/ca.crt"
qax_grpc = 'certs/qianxin.crt'

[log]
level = "info"
path = "./logs"

[redundancy_software]
software_ids = ["2000003596"]

# 业务配置
[business_config]
# 策略数量配置
strategy_num_limit = 100