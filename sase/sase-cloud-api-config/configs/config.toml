[server]
port = 8080
mode = "debug" # debug, release, test
max_export_limit = 1000
context = "/sase"
deploy_invite_url = "https://alpha-sase-test.rongma.tech/side/notice/%s"
pkg_download_url = "https://alpha-oss.rongma.tech%s"

[mongodb]
addr = 'mongodb.outside.rongma.tech:27017'
database = "alpha-rm_sase"
username = "alpha-rmclient"
password = "zo3RdOgGio*F"

[mongodb_cloud_info]
addr = 'mongodb.outside.rongma.tech:27017'
database = "cloud-info"
username = "info-cloud"
password = "000000"

[redis]
# 0-单机模式 1-集群模式
connection_mode = 0
addr = "***************:36379"
password = ""
db = 0

[nginx_minio]
# 资源前缀，桶名前缀，比如 alpha-failed-upload，那么配 alpha-
resource_prefix = ''
addr = '**************:19000'
access_key = 'BOE7OIKX386H8VI066NT'
secret_key = 's4JhD0jyxjeOxe5L6GK+KcdjVgG2A3ZuceYZgekA'
# 下载地址，不要以 / 结尾
download_url = 'https://alpha-oss.rongma.tech'


[log]
level = "info"
path = "./logs"

[email]
port = 465
host = "smtpdm.aliyun.com"
user = "<EMAIL>"
password = "abcU7vOqKRq3"
alias_name = "戎码科技"

[import_config]
user_max_size = 2000

[third_party]
address = "http://sase-org-sync-service:80"

[setting]
run_task = true
task_duration = 7200   #秒级的统计  // 定时任务执行时间  1小时执行一次
org_rps = 20   # 组织架构每秒请求第三方的次数 // 默认10次
org_sync_lock_time = 1800 # // 设置组织结构同步时的锁时长 默认1分钟
# 水印js模板路径
watermark_js_path = "/usr/local/openresty/nginx/conf/vhosts/{product_id}/js/watermark.js"

[email_subject_conf.zh]
subject = "自定义组织员工邀请"
product_name = "云镜"
alias_name = "云镜 SASE System"

[email_subject_conf.en]
subject = "Customize organization employee invitations"
product_name = "Cloud Mirror"
alias_name = "Cloud Mirror SASE System"

