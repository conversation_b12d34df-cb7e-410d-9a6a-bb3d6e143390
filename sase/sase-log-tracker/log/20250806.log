{"time":"2025-08-06T14:37:44.6630112+08:00","level":"INFO","msg":"sase-log-tracker service is running on 0.0.0.0:16003..."}
{"time":"2025-08-06T14:39:22.8446933+08:00","level":"INFO","msg":" sase-log-tracker backend service stopped."}
{"time":"2025-08-06T14:39:25.838295+08:00","level":"INFO","msg":"sase-log-tracker service is running on 0.0.0.0:16003..."}
{"time":"2025-08-06T14:40:57.4778025+08:00","level":"ERROR","msg":"bind json value error: EOF"}
{"time":"2025-08-06T14:40:57.4778025+08:00","level":"ERROR","msg":"解析请求错误:EOF"}
{"time":"2025-08-06T14:44:23.1492021+08:00","level":"INFO","msg":"任务报告成功写入Kafka - 键: connector_status_logs, 参数: &{connector_status_logs {\"batch_quarantine_file\":[{\"path\":\"C:\\\\111\\\\a.exe\",\"sha1\":\"C5798DE9EBC1107B36F0BD93700B65DA4FD8133A\",\"code\":3003}]} a16686fb23ad48a8b0f69a277d395602 rmalpha}"}
{"time":"2025-08-06T14:56:12.8866185+08:00","level":"INFO","msg":"任务报告成功写入Kafka - 键: connector_status_logs, 参数: &{connector_status_logs {\"connector_status_logs\":\"xxxxxxxxxxxxxxxxxxxxxxxx\"} a16686fb23ad48a8b0f69a277d395602 rmalpha}"}
{"time":"2025-08-06T15:19:30.4144987+08:00","level":"INFO","msg":" sase-log-tracker backend service stopped."}
