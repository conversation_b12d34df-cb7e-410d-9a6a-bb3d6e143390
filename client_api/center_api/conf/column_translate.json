{"CreateProcess": {"command_line": "进程命令行", "current_directory": "当前目录", "callstack": "调用堆栈", "havesecsection": "是否有签名段", "integritylevel": "完整性级别", "md5": "md5", "sha1": "sha1", "microsoftsignonly": "最低模块签名等级", "new_command_line": "新进程命令行", "new_process_id": "新进程ID", "new_process_name": "新进程名", "newprocess": "新进程路径", "pe.companyname": "公司名称", "pe.description": "描述", "pe.fileversion": "文件版本", "pe.internalname": "文件内部名称", "pe.productname": "产品名称", "signed": "是否签名", "signer": "签名者", "signerserial": "签名者序列号", "newprocesslevel": "新进程安全性", "processmd5": "进程md5", "processsha1": "进程sha1"}, "CreateThread": {"process": "进程路径", "process_id": "进程ID", "process_name": "进程名", "thread_id": "线程ID", "processlevel": "进程安全性", "targetprocess": "目标进程", "targetprocessid": "目标线程ID", "targetprocesslevel": "目标进程安全性", "threadstart": "线程地址"}, "LoadImage": {"process": "进程路径", "process_id": "进程ID", "process_name": "进程名", "processlevel": "进程安全性", "newimage": "镜像路径", "newimagelevel": "镜像安全性", "havesecsection": "是否有签名段", "imagebase": "镜像基址", "imagetype": "镜像类型", "signed": "是否签名", "signer": "签名者", "signerserial": "签名者序列号", "md5": "md5", "sha1": "sha1"}, "PreCreate": {"precreatedisposition": "属性", "bm_access": "访问权限", "process": "进程路径", "process_id": "进程ID", "process_name": "进程名", "processlevel": "进程安全性", "file": "文件路径", "directory": "目录"}, "Write": {"createddisposition": "属性", "process": "进程路径", "process_id": "进程ID", "process_name": "进程名", "processlevel": "进程安全性", "file": "文件路径", "directory": "目录"}, "Read": {"createddisposition": "属性", "process": "进程路径", "process_id": "进程ID", "process_name": "进程名", "processlevel": "进程安全性", "file": "文件路径", "directory": "目录"}, "WriteComplete": {"createddisposition": "属性", "process": "进程路径", "process_id": "进程ID", "process_name": "进程名", "processlevel": "进程安全性", "file": "文件路径", "directory": "目录", "md5": "md5", "signed": "是否签名", "signer": "签名者", "signerserial": "签名者序列号"}, "ReadComplete": {"createddisposition": "属性", "process": "进程路径", "process_id": "进程ID", "process_name": "进程名", "processlevel": "进程安全性", "file": "文件路径", "directory": "目录"}, "CreateFileMapping": {"createddisposition": "属性", "process": "进程路径", "process_id": "进程ID", "process_name": "进程名", "processlevel": "进程安全性", "file": "文件路径", "directory": "目录"}, "DeleteFile": {"createddisposition": "属性", "process": "进程路径", "process_id": "进程ID", "process_name": "进程名", "processlevel": "进程安全性", "file": "文件路径", "directory": "目录", "informationclass": "修改类型"}, "RenameFile": {"createddisposition": "属性", "process": "进程路径", "process_id": "进程ID", "process_name": "进程名", "processlevel": "进程安全性", "file": "文件路径", "directory": "目录", "informationclass": "修改类型", "fileex": "目标文件路径"}, "RenameToFile": {"createddisposition": "属性", "process": "进程路径", "process_id": "进程ID", "process_name": "进程名", "processlevel": "进程安全性", "file": "文件路径", "directory": "目录", "informationclass": "修改类型", "fileex": "目标文件路径"}, "LinkFile": {"createddisposition": "属性", "process": "进程路径", "process_id": "进程ID", "process_name": "进程名", "processlevel": "进程安全性", "file": "文件路径", "directory": "目录", "informationclass": "修改类型", "fileex": "目标文件路径"}, "SetInformation": {"createddisposition": "属性", "process": "进程路径", "process_id": "进程ID", "process_name": "进程名", "processlevel": "进程安全性", "file": "文件路径", "directory": "目录", "informationclass": "修改类型"}, "PreSetInformation": {"createddisposition": "属性", "process": "进程路径", "process_id": "进程ID", "process_name": "进程名", "processlevel": "进程安全性", "file": "文件路径", "directory": "目录", "informationclass": "修改类型", "basicinformationtypes": "基本信息类型"}, "PreCreateDir": {}, "PostCreateDir": {"createddisposition": "属性", "process": "进程路径", "process_id": "进程ID", "process_name": "进程名", "processlevel": "进程安全性", "file": "文件路径", "directory": "目录"}, "PreCreatePipe": {"process": "进程路径", "process_id": "进程ID", "process_name": "进程名", "processlevel": "进程安全性", "file": "文件路径", "directory": "目录"}, "PostCreatePipe": {"process": "进程路径", "process_id": "进程ID", "process_name": "进程名", "processlevel": "进程安全性", "file": "文件路径", "directory": "目录"}, "PreControlDir": {}, "PreOpenPipe": {}, "PostOpenPipe": {}, "PreOpenMailSlot": {}, "PostOpenMailSlot": {}, "PreCreateMailSlot": {}, "PostCreateMailSlot": {}, "MountVolume": {"volumedevicetype": "卷设备类型", "volumentname": "卷路径"}, "DeviceIoControl": {}, "ReleaseFileMapping": {}, "com_component": {}, "network_settings": {}, "DeleteKey": {}, "DeleteValueKey": {}, "RenameKey": {}, "SetInformationKey": {}, "QueryKey": {}, "QueryValueKey": {"process": "进程路径", "process_id": "进程ID", "process_name": "进程名", "processlevel": "进程安全性", "keyname": "注册表路径"}, "QueryMultiValueKey": {"process": "进程路径", "process_id": "进程ID", "process_name": "进程名", "processlevel": "进程安全性", "keyname": "注册表路径"}, "CreateKey": {"process": "进程路径", "process_id": "进程ID", "process_name": "进程名", "processlevel": "进程安全性", "keyname": "注册表路径", "valuename": "键值名"}, "SetValueKey": {"process": "进程路径", "process_id": "进程ID", "process_name": "进程名", "processlevel": "进程安全性", "keyname": "注册表路径", "valuename": "键值名", "valuetype": "注册表类型", "valuecontent": "键内容"}, "Other": {"process_name": "进程名字", "process": "进程路径", "process_id": "进程pid", "command_line": "进程命令行", "processlevel": "进程安全性", "httphost": "http主机名", "httpurl": "URL", "localip": "本机IP地址", "localport": "本机端口号", "remoteip": "远程IP地址", "remoteport": "远程端口号", "informationclass": "修改类型", "newimage": "镜像路径", "shellcodetypes": "shellcode类型", "sourcedll": "shellcode来源dll", "sourceoperation": "Shellcode操作", "ja3hash": "JA3指纹", "dnsquery": "Dns查询域名", "namespace": "命名空间", "wmioperation": "WMI操作", "param1name": "参数1", "param1value": "参数1值", "param2name": "参数2", "param2value": "参数2值", "param3name": "参数3", "param3value": "参数3值", "param4name": "参数4", "param4value": "参数4值", "param5name": "参数5", "param5value": "参数5值", "paramcount": "参数数量", "scriptcontent": "脚本内容", "fuzzyhash": "fuzzy hash", "rpcprocessid": "RPC进程PID", "rpcprocesslevel": "RPC进程安全性", "rpcprocessmd5": "RPC进程MD5", "rpcprocesssha1": "RPC进程Sha1", "rpcprocess": "RPC进程路径", "targetprocessmd5": "目标进程md5", "targetprocesssha1": "目标进程sha1"}}