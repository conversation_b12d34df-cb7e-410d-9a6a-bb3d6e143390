[service]
name = "center-api"
addr = "0.0.0.0:8008"
export_limit = 1000
context_path = "/"
lang = "en"
incident_export_limit = 500
is_use_new_table = true

[week_report_setting] # 周报日志配置
generate_job = "0 11 19 * * *"    # 每周一的8点定时执行

[settings]
show_headers = false
show_request = true
show_response = false
process_before_time_interval = '1d'
process_after_time_interval = '1d'
run_task = true

[process_tree]
#当节点下，取100条进程，多余不显示
max_process_num = 50
# 进程数深度
max_process_depth = 5
# 小于此数量查询两级， 超过只查询一级
child_node_num = 5
# 查询时间往前多久
before_time_interval = "15d"

[elasticsearch_setting]
# 是否需要连接es true:需要 false:不需要
switch = true

[process_statistics]
context_time = 30
cache_switch = true
redis_cache_time = "10m"

# 开启协程的数量
[set_goroutine_chan]
incident_time_line_num = 5
instruction_concurrency_num = 5

[nats_default]
switch = false

[kafka_setting]
switch = true

[auto_response_setting]
switch = true

[notify_setting]
switch = true

[minio_upgrade_ext]
part_size = 104857600
# 升级包上传的分片的大小  默认是50M, 如果不配置，走代码中的默认50M的数据

[instruction_setting]
# 升级间隔时间
upgrade_interval_time = 300

[file_level]
isolate_file_level = 15
ioc_prevention_level = 75

[awss3_corpus]
switch = true
