[service]
name = 'client_instruction'
addr = '0.0.0.0:16001'
encrypt = true
# 执行同步数量
execute_virus_num = 5

[logging]
level = 'error'

[actlogger]
debug = true

[kafka_setting]
switch = true

[execute_plan]
# 是否开启缓存
switch = false
# 计划缓存时间（分）
execute_plan_cache_time = 5

[kafka_topic_map]
compliance_inspection = 'policy-task-report'
vulnerability_scan = 'policy-task-report'
peripheral_control = 'policy-task-report'
firewall = 'policy-task-report'
behavior_control = 'policy-task-report'
distribute_software = 'policy-task-report'
software_control = 'policy-task-report'
distribute_file = 'policy-task-report'
software = 'policy-task-report'
sensitive_data = 'policy-task-report'
terminal_discovery= 'policy-task-report'
app_discovery = 'policy-task-report'