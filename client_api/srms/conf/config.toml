[service]
name = 'rmedr_srms'
addr = '0.0.0.0:6001'
upload_switch = "on"
file_query_rank = true
check_level = [0, 10, 20, 30, 40, 50, 60, 70]
request_length_threshold = 10240

[logging]
type = 'json'
dir = 'log'
level = 'info'
stdout = true

[mongodb_default]
# direct 是否直连，一般开发阶段本地连测试服务器改成 true
# 发到 alpha alpha-saas 及以上环境，不要这项，直接删除
direct = true

[actlogger]
debug = true

[uploads]
quantity = 10
interval_time = 10
api_interval_time = 10
