[service]
name = "client-task"
preduce_task_switch = true
# task 定时间隔时间
minio_failed_upload_read_time = '10m'
# task_time 分布到第二天2点到N秒随机
preduce_task_time_delay = 10800
# task 处理协程数量
minio_failed_upload_coroutines_num = 20
# 补日志时，只获取 N 小时前的，N>0时，才限制
failed_upload_read_time_ago = 24
# dump文件上传合并读取数据时间间隔
file_dump_task_read_time = '1m'
# 文件合并并发进程数
file_dump_task_coroutines_num = 5
# 允许的非log头，头是log的不用设置
allow_log_headers = ["process"]
# 日志块协程数量
log_block_channels_num = 25

[settings]
batch_write_size = 25

[switch]
complement_log = true
merge_dump_file = true

[logging]
level = "error"

# [] 中括号里的值需要跟公共配置一致
[minio_default]
# 是否上传 minio
switch = false

[actlogger]
debug = true
