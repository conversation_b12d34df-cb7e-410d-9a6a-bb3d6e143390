{"process.I_ProcessId": "I_ProcessId", "process.S_ProcessUuid": "S_ProcessUuid", "process.B64_Process": "B64_Process", "process.B64_CommandLine": "B64_CommandLine", "process.S_ProcessMd5": "S_ProcessMd5", "process.S_ProcessSha1": "S_ProcessSha1", "process.I_ProcessLevel": "I_ProcessLevel", "process.I_ProcessTreeLevel": "I_ProcessTreeLevel", "process.S_ProcessIntegrity": "S_ProcessIntegrity", "process.S_ProcessSigned": "S_ProcessSigned", "process.B64_ProcessSignerExEx": "B64_ProcessSignerExEx", "process.S_ProcessSignerSerialEx": "S_ProcessSignerSerialEx", "process.S_PsFileSignType": "S_PsFileSignType", "process.I_ProcessCreateTime": "T_ProcessCreateTime", "process.S_PE.FileVersion": "S_Process_PE.FileVersion", "process.S_PE.InternalName": "S_Process_PE.InternalName", "process.S_PE.Description": "S_Process_PE.Description", "process.S_PE.ProductName": "S_Process_PE.ProductName", "process.S_PE.CompanyName": "S_Process_PE.CompanyName", "process.I_MaxProcessDllLevel": "I_MaxProcessDllLevel", "process.S_RootProcessUuid": "S_process_RootProcessUuid", "process.I_RootProcessId": "I_process_RootProcessId", "process.B64_RootProcess": "B64_process_RootProcess", "process.S_RootProcessMd5": "S_process_RootProcessMd5", "process.S_RootProcessSha1": "S_process_RootProcessSha1", "process.S_PsTreeSignType": "S_PsTreeSignType", "parent_process.I_ProcessId": "I_parent_ProcessId", "parent_process.S_ProcessUuid": "S_parent_ProcessUuid", "parent_process.B64_Process": "B64_parent_Process", "parent_process.S_ProcessMd5": "S_parent_ProcessMd5", "parent_process.S_ProcessSha1": "S_parent_ProcessSha1", "parent_process.I_ProcessCreateTime": "T_parent_ProcessCreateTime", "grand_process1.I_ProcessId": "I_grand1_ProcessId", "grand_process1.S_ProcessUuid": "S_grand1_ProcessUuid", "grand_process1.B64_Process": "B64_grand1_Process", "grand_process1.S_ProcessMd5": "S_grand1_ProcessMd5", "grand_process1.S_ProcessSha1": "S_grand1_ProcessSha1", "grand_process1.I_ProcessCreateTime": "T_grand1_ProcessCreateTime", "grand_process2.I_ProcessId": "I_grand2_ProcessId", "grand_process2.S_ProcessUuid": "S_grand2_ProcessUuid", "grand_process2.B64_Process": "B64_grand2_Process", "grand_process2.S_ProcessMd5": "S_grand2_ProcessMd5", "grand_process2.S_ProcessSha1": "S_grand2_ProcessSha1", "grand_process2.I_ProcessCreateTime": "T_grand2_ProcessCreateTime"}