[service]
name = 'logreport'
http = '0.0.0.0:5003'
context_path = '/'
# IP 黑名单，优先级最高，多个用英文半角逗号间隔
client_ip_blacklist = ""
client_id_whitelist = ""
# proportion 通过比例，例如：100 就是 100% 处理数据
proportion = 100
threshold_limit = 120
threshold_minute = 1
# 请求协程数量
coroutine_num = 4000
# 日志块协程数量
log_block_channels_num = 25
# 允许的非log头，头是log的不用设置
allow_log_headers = ["process"]

[settings]
show_request = false
show_response = false
encode_response = true
batch_write_size = 25

[logging]
level = "error"

[elasticsearch_error]
# 写es的开关
switch = true

[elasticsearch_writer]
# 写es的开关
switch = true

# [] 中括号里的值需要跟公共配置一致
[minio_default]
# 是否上传 minio
switch = false

[actlogger]
debug = true
