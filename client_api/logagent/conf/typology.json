[{"process-node": [{"and": [{"where": "module", "op": "==", "value": "PS"}, {"where": "fltrid", "op": "in", "value": [1, 100, 99999]}]}]}, {"process-info": [{"and": [{"where": "module", "op": "in", "value": ["PS", "Ob<PERSON><PERSON><PERSON>", "Advanced", "ProcessPerformance"]}]}]}, {"rmcore": [{"and": [{"where": "module", "op": "in", "value": ["rmcore", "evtconsumer", "etwconsumer", "rm_script_engine"]}]}]}, {"file": [{"and": [{"where": "module", "op": "==", "value": "File"}]}]}, {"network": [{"and": [{"where": "module", "op": "==", "value": "WFP"}]}]}, {"registry": [{"and": [{"where": "module", "op": "==", "value": "<PERSON><PERSON><PERSON><PERSON>"}]}]}, {"system": [{"and": [{"where": "module", "op": "in", "value": ["SysConsumer", "EvtConsumer", "RpcService", "Misc", "Condrv"]}]}]}]