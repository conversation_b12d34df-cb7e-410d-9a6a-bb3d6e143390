[{"remove": {"field": "cmd", "ignore_missing": true}}, {"remove": {"field": "Id", "ignore_missing": true}}, {"remove": {"field": "Org", "ignore_missing": true}}, {"remove": {"field": "org", "ignore_missing": true}}, {"rename": {"field": "processid", "target_field": "process_id", "ignore_missing": true}}, {"rename": {"field": "commandline", "target_field": "command_line", "ignore_missing": true}}, {"rename": {"field": "newprocessid", "target_field": "new_process_id", "ignore_missing": true}}, {"basename": {"field": "process", "target_field": "process_name", "ignore_missing": true}}, {"lowercase": {"field": "process_name", "ignore_missing": true}}, {"basename": {"field": "newprocess", "target_field": "new_process_name", "ignore_missing": true}}, {"lowercase": {"field": "new_process_name", "ignore_missing": true}}, {"basename": {"field": "parent_process", "target_field": "parent_process_name", "ignore_missing": true}}, {"lowercase": {"field": "parent_process_name", "ignore_missing": true}}, {"basename": {"field": "grand1_process", "target_field": "grand1_process_name", "ignore_missing": true}}, {"lowercase": {"field": "grand1_process_name", "ignore_missing": true}}, {"basename": {"field": "grand2_process", "target_field": "grand2_process_name", "ignore_missing": true}}, {"lowercase": {"field": "grand2_process_name", "ignore_missing": true}}, {"basename": {"field": "process_rootprocess", "target_field": "process_rootprocess_name", "ignore_missing": true}}, {"lowercase": {"field": "process_rootprocess_name", "ignore_missing": true}}, {"copy": {"field": "access", "new_field": "bm_access", "type": "int64", "ignore_missing": true}}, {"bitmask": {"field": "bm_access", "flags": ["file-special", "file-generic"], "if": [{"and": [{"where": "operation", "op": "==", "value": "PreCreate"}]}], "ignore_missing": true}}, {"bitmask": {"field": "bm_access", "flags": ["file-special", "dir-generic"], "if": [{"and": [{"where": "operation", "op": "==", "value": "PreCreateDir"}]}], "ignore_missing": true}}, {"copy": {"field": "desiredaccess", "new_field": "bm_desiredaccess", "type": "int64", "ignore_missing": true}}, {"bitmask": {"field": "bm_desiredaccess", "flags": ["process-special", "process-generic"], "if": [{"and": [{"where": "operation", "op": "in", "value": ["OpenProcess", "DupProcess"]}]}], "ignore_missing": true}}, {"bitmask": {"field": "bm_desiredaccess", "flags": ["thread-special", "thread-generic"], "if": [{"and": [{"where": "operation", "op": "in", "value": ["OpenThread", "DupThread"]}]}], "ignore_missing": true}}, {"bitmask": {"field": "bm_desiredaccess", "flags": ["service-special", "service-generic"], "if": [{"and": [{"where": "operation", "op": "in", "value": ["OpenService", "CreateService"]}]}], "ignore_missing": true}}, {"bitmask": {"field": "bm_desiredaccess", "flags": ["file-special", "file-generic"], "if": [{"and": [{"where": "operation", "op": "in", "value": ["OpenPipe", "PreOpenPipe", "PostOpenPipe"]}]}], "ignore_missing": true}}, {"lowercase": {"field": "Domain", "ignore_missing": true}}, {"convert": {"field": "ErrorControl", "type": "long", "ignore_missing": true, "ignore_failure": true}}, {"rename": {"field": "newcommandline", "target_field": "new_command_line", "ignore_missing": true}}, {"copy": {"field": "newprotect", "new_field": "bm_newprotect", "type": "int64", "ignore_missing": true}}, {"bitmask": {"field": "bm_newprotect", "flags": ["protect-generic"], "if": [{"and": [{"where": "operation", "op": "in", "value": ["NtProtectVirtualMemory", "NtMapViewOfSection"]}]}], "ignore_missing": true}}, {"copy": {"field": "oldprotect", "new_field": "bm_oldprotect", "type": "int64", "ignore_missing": true}}, {"bitmask": {"field": "bm_oldprotect", "flags": ["protect-generic"], "if": [{"and": [{"where": "operation", "op": "in", "value": ["NtProtectVirtualMemory", "NtMapViewOfSection"]}]}], "ignore_missing": true}}, {"copy": {"field": "type", "new_field": "bm_type", "type": "int64", "ignore_missing": true}}, {"bitmask": {"field": "bm_type", "flags": ["memory-generic"], "if": [{"and": [{"where": "operation", "op": "in", "value": ["NtQueueApcThread"]}]}], "ignore_missing": true}}, {"copy": {"field": "win32protect", "new_field": "bm_win32protect", "type": "int64", "ignore_missing": true}}, {"bitmask": {"field": "bm_win32protect", "flags": ["protect-generic"], "if": [{"and": [{"where": "operation", "op": "in", "value": ["NtProtectVirtualMemory", "NtMapViewOfSection"]}]}], "ignore_missing": true}}, {"convert": {"field": "pubexp", "type": "long", "ignore_missing": true, "ignore_failure": true}}, {"set": {"field": "category", "value": "{{@scope}}"}}, {"set": {"field": "@raw", "value": "{{@raw}}"}}, {"hash": {"field": "@raw", "target_field": "@hash", "method": "md5", "with_timestamp": true}}, {"set": {"field": "@timestamp", "value": "{{@timestamp}}"}}, {"datetime": {"field": "@timestamp", "format": "NANO_UTC", "ignore_missing": false}}, {"del": {"field": "@raw"}}, {"del": {"field": "treelinks"}}]