[service]
name = "logagent"

[dispatch]
# 需要跟 elasticsearch_writers 顺序对应上
scopes = [
    "*",
    "file",
    "process-info",
    "process-node",
    "network",
    "registry",
    "rmcore",
    "system",
]
retries = 1
routines = 40

[settings]
process_ttl = "1d"

[logging]
level = "debug"

[elasticsearch_error]
# 写es的开关
switch = true

[[elasticsearch_writers]]
# 写的开关
switch = false

[[elasticsearch_writers]]
# 写的开关
switch = false

[[elasticsearch_writers]]
# 写的开关
switch = false

[[elasticsearch_writers]]
# 写的开关
switch = false

[[elasticsearch_writers]]
# 写的开关
switch = false

[[elasticsearch_writers]]
# 写的开关
switch = false

[[elasticsearch_writers]]
# 写的开关
switch = false

[[elasticsearch_writers]]
# 写的开关
switch = false
