[service]
name = 'instruction-internal'
addr = '0.0.0.0:16002'
context_path = '/'

[settings]
# 是否启动任务
run_task = false
# 不需要运行的任务
not_run_tasks = []
# 多个pod启动时需要改为true
task_redis_lock_switch = false
# 任务每页获取的任务数量
instruction_task_page_num = 500
# 每页任务最多下发的主机数量
instruction_task_page_host_num = 100
# 获取完一页任务后等待时间
instruction_task_sleep_interval = "10s"
# 每个任务id下发的间隔时间
instruction_task_id_interval = "20m"
# 每个主机发送任务的间隔
instruction_host_send_interval = "10s"
# 每个主机每次发送的任务数量
instruction_host_task_num = 10

[instruction_finish_type_map]
# 任务获取成功，即任务成功的任务类型
instruction_name_map = [
    "uninst_agent",
    "update_agent_files"
]