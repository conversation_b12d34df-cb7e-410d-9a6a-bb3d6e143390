[service]
name = 'rm_gateway'
http = '0.0.0.0:5003'
mode = "debug"
context_path = '/client/gateway/'
# is_saas 设置是否是 saas，私有化部署，不加这个配置项
is_saas = true

[private_keys]
rsa_2048 = 'certs/rsa2048.prvkey'
rsa_4096 = 'certs/rsa4096.prvkey'

[settings]
show_request = true
show_response = true
encode_response = true

[hosts]
# 下面都是客户端云地址，default 是没找到的情况
beta = '[{"domain":"alpha-console-api.rongma.tech","tcp_port":80,"udp_port":0},{"domain":"alpha-console-api.rongma.tech","tcp_port":443,"udp_port":0}]'
qax = '[{"domain":"alpha-console-api.rongma.tech","tcp_port":80,"udp_port":0},{"domain":"alpha-console-api.rongma.tech","tcp_port":443,"udp_port":0}]'
default = '[{"domain":"alpha-console-api.rongma.tech","tcp_port":80,"udp_port":0},{"domain":"alpha-console-api.rongma.tech","tcp_port":443,"udp_port":0}]'

[logging]
level = "warn"

[redis]
addr = '***************:36379'
password = ''
db   = 0
queue = ''

[actlogger]
debug = true
actlog_index_name = "alpha-actlog"
