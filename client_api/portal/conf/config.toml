[service]
name = 'portal'
http = '0.0.0.0:5002'
grpc = '0.0.0.0:6000'
context_path = '/'
# channel 活跃更新后多久删除（秒）
channel_del_time = 172800
# 客户端后续接口请求地址
req_host = [
    'https:domain:alpha-console-api.rongma.tech:443',
    'http:domain:alpha-console-api.rongma.tech:80',
]

[settings]
show_headers = false
show_request = false
show_response = false
encode_response = true

[mongodb_default]
# direct 是否直连，一般开发阶段本地连测试服务器改成 true
# 发到 alpha alpha-saas 及以上环境，不要这项，直接删除
direct = true

[logging]
level = "error"

# [] 中括号里的值需要跟公共配置一致
[nats_default]
switch = true

[security]
token_expires = '30d'
channel_expires = '30d'

[actlogger]
debug = true
