# 前端机公共配置文件
#
# 注意编写规范
#
# 遇到问题，及时联系以下人员
#
# 研发：
# <AUTHOR> <EMAIL>
#
# 运维：
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>

############# 服务基础配置 ##################

[service]
mode = 'debug'
ccs_name = 'alpha'
# is_saas 设置是否是 saas，私有化部署，不加这个配置项
is_saas = true
data_retention_days = 90
############# 服务基础配置 ##################

############# MySQL 配置 ##################

[mysql_default]
# 资源前缀，库名前缀，比如 alpha-portal，那么配 alpha-
[mysql]
addr = "192.168.111.195:30336"
database = "passport"
username = "root"
password = "123"
charset = "utf8mb4"

############# MySQL 配置 ##################

############# ClickHouse 配置 ##################

[clickhouse_default]
# 资源前缀，库名前缀，比如 alpha-portal，那么配 alpha-
resource_prefix = 'alpha_saas_'
addr = 'clickhouse.outside.rongma.tech:19000'
username = "user1"
password = "123456"

############# ClickHouse 配置 ##################

############# Mongodb 配置 ##################

[mongodb_default]
# 资源前缀，库名前缀，比如 alpha-portal，那么配 alpha-
resource_prefix = 'alpha-saas-'
addr = 'mongodb.outside.rongma.tech:27017'
username = 'alpha-saas-rmclient'
password = 'zo3RdOgGio*F'

[mongodb_console_proxy]
# 资源前缀，库名前缀，比如 alpha-portal，那么配 alpha-
resource_prefix = 'alpha-saas-'
addr = 'mongodb.outside.rongma.tech:27017'
username = 'alpha-saas-rmclient'
password = 'zo3RdOgGio*F'

[mongodb_cloud_upgrade]
resource_prefix = 'alpha-saas-'
addr = 'mongodb.outside.rongma.tech:27017'
username = 'alpha-saas-rmclient'
password = 'zo3RdOgGio*F'

[mongodb_engine]
resource_prefix = 'alpha-saas-'
addr = 'mongodb.outside.rongma.tech:27017'
#addr = '127.0.0.1:27017'
username = 'alpha-saas-rmclient'
password = 'zo3RdOgGio*F'

[mongodb_group]
resource_prefix = 'alpha-saas-'
addr = 'mongodb.outside.rongma.tech:27017'
username = 'alpha-saas-rmclient'
password = 'zo3RdOgGio*F'

[mongodb_sase_report]
resource_prefix = 'alpha-saas-'
addr = 'mongodb.outside.rongma.tech:27017'
username = 'alpha-saas-rmclient'
password ='zo3RdOgGio*F'
############# Mongodb 配置 ##################

############# 各种证书配置 ##################

[certs]
rsa_2048 = 'certs/rsa2048.prvkey'
rsa_4096 = 'certs/rsa4096.prvkey'
crt = "certs/client.crt"
key = "certs/client.key"
ca = "certs/ca.crt"
qax_grpc = 'certs/qianxin.crt'

############# 各种证书配置 ##################

############# Grpc 配置 ##################

[grpc_info]
# 是否验证证书
is_cheack_cert = true
addr = 'alpha-saas-grpc.rongma.tech:8443'

[grpc_srms]
# 是否验证证书
is_cheack_cert = true
addr = 'alpha-saas-grpc.rongma.tech:8443'

[grpc_instructions]
# 是否验证证书
#is_cheack_cert = true
#addr = 'alpha-saas-grpc.rongma.tech:8443'
is_cheack_cert = false
addr = '192.168.111.76:5000'

[grpc_upgrade]
# 是否验证证书
is_cheack_cert = true
addr = 'alpha-saas-grpc.rongma.tech:8443'

[grpc_portal]
# 是否验证证书
is_cheack_cert = false
addr = '127.0.0.1:6000'

[grpc_file]
# 是否验证证书
is_cheack_cert = false
addr = '192.168.111.195:38889'

[grpc_engine_query]
# 是否验证证书
is_cheack_cert = false
addr = 'engine-query.engine.svc.cluster.local:80'

[grpc_qianxin]
# 是否验证证书
is_cheack_cert = true
addr = 'styxcl.b.qianxin.com:8443'

[grpc_sample]
is_cheack_cert = false
addr = "rmsample.client.svc.cluster.local:6060"

[grpc_group]
# 是否验证证书
is_cheack_cert = false
addr = 'rm-group.client.svc.cluster.local:6060'

[grpc_client_strategy]
is_cheack_cert = false
addr = 'strategy-grpc.client.svc.cluster.local:6060'

############# Grpc 配置 ##################

############# Redis 配置 ##################

[redis_default]
# connection_mode 连接方式，0 为单机模式，1 为集群模式
connection_mode = 0
addr = ['192.168.111.195:36379']
password = ''
db = 0

############# Redis 配置 ##################

############# Nats 配置 ##################

[nats_default]
addr = ['192.168.111.195:34222']
username = ''
password = ''

############# Nats 配置 ##################

############# Minio 配置 ##################

[minio_default]
# 资源前缀，桶名前缀，比如 alpha-failed-upload，那么配 alpha-
resource_prefix = 'alpha-saas-'
addr = 'minio-alpha-saas.outside.rongma.tech:9000'
access_key = 'BOE7OIKX386H8VI066NT'
secret_key = 's4JhD0jyxjeOxe5L6GK+KcdjVgG2A3ZuceYZgekA'
# 下载地址，不要以 / 结尾
download_url = 'https://alpha-oss.rongma.tech'

[minio_01]
# 资源前缀，桶名前缀，比如 alpha-failed-upload，那么配 alpha-
resource_prefix = 'alpha-saas-'
addr = 'minio-alpha-saas.outside.rongma.tech:9000'
access_key = 'BOE7OIKX386H8VI066NT'
secret_key = 's4JhD0jyxjeOxe5L6GK+KcdjVgG2A3ZuceYZgekA'
# 下载地址，不要以 / 结尾
download_url = 'https://alpha-oss.rongma.tech'

[minio_corpus]
# 资源前缀，桶名前缀，比如 alpha-failed-upload，那么配 alpha-
resource_prefix = 'alpha-saas-'
addr = 'minio-alpha-saas.outside.rongma.tech:9000'
access_key = 'BOE7OIKX386H8VI066NT'
secret_key = 's4JhD0jyxjeOxe5L6GK+KcdjVgG2A3ZuceYZgekA'
# 下载地址，不要以 / 结尾
download_url = 'https://alpha-oss.rongma.tech'

############# Minio 配置 ##################

############# Awss3 配置 ##################
[awss3_default]
resource_prefix = 'alpha-'
endpoint = 'https://oss-yg-zzbm.yun.qianxin.com:443'
access_key = 'ZZTIYXNWQJ2UCFL0TGBT'
secret_key = '5ct84sm6xNTzvMrkBvcSAo4sK6C2AcnhcTkvdzdt'
region ='oss-zzbm-yg'

[awss3_corpus]
resource_prefix = 'alpha-'
endpoint = 'https://oss-yg-zzbm.yun.qianxin.com:443'
access_key = 'ZZTIYXNWQJ2UCFL0TGBT'
secret_key = '5ct84sm6xNTzvMrkBvcSAo4sK6C2AcnhcTkvdzdt'
region ='oss-zzbm-yg'

############# Kafka 配置 ##################

[kafka_default]
# 资源前缀，topic 前缀，比如 alpha-preduce-event，那么配 alpha-
resource_prefix = 'alpha-saas-'
addr = [
    'kafka0.outside.rongma.tech:19092',
    'kafka1.outside.rongma.tech:29092',
    'kafka2.outside.rongma.tech:39092',
]
username = ''
password = ''

[kafka_engine]
# 资源前缀，topic 前缀，比如 alpha-preduce-event，那么配 alpha-
resource_prefix = 'alpha-saas-'
addr = ['kafka-alpha.middleware.svc.cluster.local:9092']
username = ''
password = ''
write_max_batch_bytes=20971520

[kafka_rmgroup]
# 资源前缀，topic 前缀，比如 alpha-preduce-event，那么配 alpha-
resource_prefix = 'alpha-saas-'
addr = ['kafka-alpha-saas.middleware.svc.cluster.local:9092']
username = ''
password = ''
write_max_batch_bytes=20971520

############# Kafka 配置 ##################

############# ElasticSearch 配置 ##################

[elasticsearch_default]
# 资源前缀，索引前缀，比如 alpha-ratp-events-v1-file，那么配 alpha-
resource_prefix = 'alpha-saas-'
addr = ['http://elasticsearch.outside.rongma.tech:9200']
username = 'elastic'
password = 'elastic'
pipeline = ''

[elasticsearch_engine]
resource_prefix = 'alpha-saas-'
addr = ['http://elasticsearch.outside.rongma.tech:92000']
username = 'elastic'
password = 'elastic'
pipeline = ''


############# ElasticSearch 配置 ##################

[actlogger]
actlog_index_name = "alpha-saas-actlog"