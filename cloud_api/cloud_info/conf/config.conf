[service]
name = 'cloud_client_info'
grpc = '0.0.0.0:5001'
mode = "debug"

[settings]
show_request = true
encode_response = true

[logging]
level = "error"

[mongodb]
addr = 'mongodb.outside.rongma.tech:27017'
#addr = '192.168.111.18:32718'
database = "cloud-info"
#database = "cloud_info"
#username = "cloud"
#password = "RYI5*6!odrow$CLJi"
username = "info-cloud"
password = "000000"
direct = true

[trantor_jarvis_grpc]
addr = "127.0.0.1:8894"

[trantor_terminal_grpc]
addr = "127.0.0.1:8892"

[redis]
addr = '192.168.111.185:36379'
password = ''
db   = 0
queue = ''

[elasticsearch]
addr = ['http://elasticsearch.outside.rongma.tech:9200']
username = 'elastic'
password = 'elastic'

[[elasticsearch.writers]]
name = "metrics"
index = 'alpha-saas-ratp-metrics'
