[service]
name = 'rm_gateway'
http = '0.0.0.0:5005'
mode = "debug"
context_path = '/client/gateway/'
# is_saas 设置是否是 saas，私有化部署，不加这个配置项
is_saas = true

[private_keys]
rsa_2048 = 'certs/rsa2048.prvkey'
rsa_4096 = 'certs/rsa4096.prvkey'

[settings]
show_request = true
show_response = true
encode_response = false

[hosts]
# 下面都是客户端云地址，default 是没找到的情况
beta = '[{"domain":"css-i-alpha.rongma.tech","tcp_port":80,"udp_port":0},{"domain":"css-i-alpha.rongma.tech","tcp_port":443,"udp_port":0},{"domain":"css-o-alpha.rongma.tech","tcp_port":80,"udp_port":0},{"domain":"css-o-alpha.rongma.tech","tcp_port":443,"udp_port":0}]'
qax = '[{"domain":"css-i-alpha.rongma.tech","tcp_port":80,"udp_port":0},{"domain":"css-i-alpha.rongma.tech","tcp_port":443,"udp_port":0},{"domain":"css-o-alpha.rongma.tech","tcp_port":80,"udp_port":0},{"domain":"css-o-alpha.rongma.tech","tcp_port":443,"udp_port":0}]'
# mysql r_org_app 表，org_name 对应的 dcs_id = 4 时，返回 overseas 配置
overseas = '[{"domain":"yilong-api.rongma.cn","tcp_port":80,"udp_port":0},{"domain":"yilong-api.rongma.cn","tcp_port":443,"udp_port":0}]'
default = '[{"domain":"css-i-alpha-saas.rongma.tech","tcp_port":80,"udp_port":0},{"domain":"css-i-alpha-saas.rongma.tech","tcp_port":443,"udp_port":0},{"domain":"css-o-alpha-saas.rongma.tech","tcp_port":80,"udp_port":0},{"domain":"css-o-alpha-saas.rongma.tech","tcp_port":443,"udp_port":0}]'

[logging]
level = "warn"

[passport_mysql]
addr = "192.168.111.195:30336"
database = "passport"
username = "root"
password = "123"
charset = "utf8mb4"

[redis]
addr = '192.168.111.185:36379'
password = ''
db   = 0
queue = ''

[actlogger]
debug = false
actlog_index_name = "alpha-actlog"
