func (r *InstructionsRepository) GetProcessSecurityLevel(request *rmmd5.BatchGetMd5InfoRequest) map[string]uint32 {
	var levelMap sync.Map
	processLevel := make(map[string]uint32)
	// 分割数据
	chunkSize := 20

	chanNum := conf.SetGoroutineChan.InstructionConcurrencyNum
	if chanNum == 0 {
		chanNum = 1
	}

	sem := make(chan struct{}, chanNum)
	// 用于等待所有协程完成
	var wg sync.WaitGroup

	for i := 0; i < len(request.RequestCon); i += chunkSize {
		end := i + chunkSize
		if end > len(request.RequestCon) {
			end = len(request.RequestCon)
		}

		sem <- struct{}{}
		var securityLevel []*rmmd5.GetMd5InfoReplyDatas
		wg.Add(1)
		go func(chunk []*rmmd5.RequestCon) {
			// 释放信号量
			defer func() {
				<-sem
			}()
			defer wg.Done()
			// 发起请求
			request.RequestCon = chunk
			securityLevel = r.GetSecurityLevel(request)
			for key, val := range securityLevel {
				var i = 0
				for _, item := range chunk {
					if i == key {
						levelMap.Store(item.Md5, val.GetSecurity().GetLevel())
					}
					i++
				}
			}
		}(request.RequestCon[i:end])
	}

	// 等待所有协程完成
	wg.Wait()

	levelMap.Range(func(key, value interface{}) bool {
		processLevel[cast.ToString(key)] = cast.ToUint32(value)
		return true
	})

	return processLevel
}