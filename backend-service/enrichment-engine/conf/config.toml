[service]
name = 'enrichment-engine'
request_mode = 'consumer'
debug = true
routines = 1
ruletype = 'list'
type = "Enrichment"
is_normal = true
proportion = 100

[mongodb_default]
# direct 是否直连，一般开发阶段本地连测试服务器改成 true
# 发到 alpha alpha-saas 及以上环境，不要这项，直接删除
direct = true

[log]
type = 'json'
dir = 'log'
level = 'error'
stdout = true

[kafka_reader]
switch = true

[kafka_writer]
switch = true

[clickhouse_default]
switch = true
