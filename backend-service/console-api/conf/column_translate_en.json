{"CreateProcess": {"command_line": "command line", "current_directory": "current directory", "callstack": "call stack", "havesecsection": "has signature section", "integritylevel": "integrity level", "md5": "md5", "sha1": "sha1", "microsoftsignonly": "Microsoft sign only", "new_command_line": "new command line", "new_process_id": "new process id", "new_process_name": "new process name", "newprocess": "new process path", "pe.companyname": "company name", "pe.description": "description", "pe.fileversion": "file version", "pe.internalname": "file internal name", "pe.productname": "product name", "signed": "signed", "signer": "signer", "signerserial": "signer serial", "newprocesslevel": "new process security", "processmd5": "process md5", "processsha1": "process sha1"}, "CreateThread": {"process": "process path", "process_id": "process id", "process_name": "process name", "thread_id": "thread id", "processlevel": "process security", "targetprocess": "target process", "targetprocessid": "target process id", "targetprocesslevel": "target process security", "threadstart": "thread start"}, "LoadImage": {"process": "process path", "process_id": "process id", "process_name": "process name", "processlevel": "process security", "newimage": "new image", "newimagelevel": "new image security", "havesecsection": "has signature section", "imagebase": "image base", "imagetype": "image type", "signed": "signed", "signer": "signer", "signerserial": "signer serial", "md5": "md5", "sha1": "sha1"}, "PreCreate": {"precreatedisposition": "attributes", "bm_access": "access permissions", "process": "process path", "process_id": "process id", "process_name": "process name", "processlevel": "process security", "file": "file path", "directory": "directory"}, "Write": {"createddisposition": "attributes", "process": "process path", "process_id": "process id", "process_name": "process name", "processlevel": "process security", "file": "file path", "directory": "directory"}, "Read": {"createddisposition": "attributes", "process": "process path", "process_id": "process id", "process_name": "process name", "processlevel": "process security", "file": "file path", "directory": "directory"}, "WriteComplete": {"createddisposition": "attributes", "process": "process path", "process_id": "process id", "process_name": "process name", "processlevel": "process security", "file": "file path", "directory": "directory", "md5": "md5", "signed": "signed", "signer": "signer", "signerserial": "signer serial"}, "ReadComplete": {"createddisposition": "attributes", "process": "process path", "process_id": "process id", "process_name": "process name", "processlevel": "process security", "file": "file path", "directory": "directory"}, "CreateFileMapping": {"createddisposition": "attributes", "process": "process path", "process_id": "process id", "process_name": "process name", "processlevel": "process security", "file": "file path", "directory": "directory"}, "DeleteFile": {"createddisposition": "attributes", "process": "process path", "process_id": "process id", "process_name": "process name", "processlevel": "process security", "file": "file path", "directory": "directory", "informationclass": "information class"}, "RenameFile": {"createddisposition": "attributes", "process": "process path", "process_id": "process id", "process_name": "process name", "processlevel": "process security", "file": "file path", "directory": "directory", "informationclass": "information class", "fileex": "target file path"}, "RenameToFile": {"createddisposition": "attributes", "process": "process path", "process_id": "process id", "process_name": "process name", "processlevel": "process security", "file": "file path", "directory": "directory", "informationclass": "information class", "fileex": "target file path"}, "LinkFile": {"createddisposition": "attributes", "process": "process path", "process_id": "process id", "process_name": "process name", "processlevel": "process security", "file": "file path", "directory": "directory", "informationclass": "information class", "fileex": "target file path"}, "SetInformation": {"createddisposition": "attributes", "process": "process path", "process_id": "process id", "process_name": "process name", "processlevel": "process security", "file": "file path", "directory": "directory", "informationclass": "information class"}, "PreSetInformation": {"createddisposition": "attributes", "process": "process path", "process_id": "process id", "process_name": "process name", "processlevel": "process security", "file": "file path", "directory": "directory", "informationclass": "information class", "basicinformationtypes": "basic information types"}, "PreCreateDir": {}, "PostCreateDir": {"createddisposition": "attributes", "process": "process path", "process_id": "process id", "process_name": "process name", "processlevel": "process security", "file": "file path", "directory": "directory"}, "PreCreatePipe": {"process": "process path", "process_id": "process id", "process_name": "process name", "processlevel": "process security", "file": "file path", "directory": "directory"}, "PostCreatePipe": {"process": "process path", "process_id": "process id", "process_name": "process name", "processlevel": "process security", "file": "file path", "directory": "directory"}, "PreControlDir": {}, "PreOpenPipe": {}, "PostOpenPipe": {}, "PreOpenMailSlot": {}, "PostOpenMailSlot": {}, "PreCreateMailSlot": {}, "PostCreateMailSlot": {}, "MountVolume": {"volumedevicetype": "volume device type", "volumentname": "volume path"}, "DeviceIoControl": {}, "ReleaseFileMapping": {}, "com_component": {}, "network_settings": {}, "DeleteKey": {}, "DeleteValueKey": {}, "RenameKey": {}, "SetInformationKey": {}, "QueryKey": {}, "QueryValueKey": {"process": "process path", "process_id": "process id", "process_name": "process name", "processlevel": "process security", "keyname": "key"}, "QueryMultiValueKey": {"process": "process path", "process_id": "process id", "process_name": "process name", "processlevel": "process security", "keyname": "key"}, "CreateKey": {"process": "process path", "process_id": "process id", "process_name": "process name", "processlevel": "process security", "keyname": "key", "valuename": "value name"}, "SetValueKey": {"process": "process path", "process_id": "process id", "process_name": "process name", "processlevel": "process security", "keyname": "key", "valuename": "value name", "valuetype": "value type", "valuecontent": "value content"}, "Other": {"process_name": "process name", "process": "process path", "process_id": "process id", "command_line": "command line", "processlevel": "process security", "httphost": "http host name", "httpurl": "URL", "localip": "local ip", "localport": "local port", "remoteip": "remote ip", "remoteport": "remote port", "informationclass": "information class", "newimage": "new image", "shellcodetypes": "shellcode types", "sourcedll": "shellcode source dll", "sourceoperation": "shellcode source operation", "ja3hash": "ja3 hash", "dnsquery": "dns query", "namespace": "name space", "wmioperation": "wmi operation", "param1name": "Parameter 2", "param1value": "Parameter 1 Value", "param2name": "Parameter 2", "param2value": "Parameter 2 Value", "param3name": "Parameter 3", "param3value": "Parameter 3 Value", "param4name": "Parameter 4", "param4value": "Parameter 4 Value", "param5name": "Parameter 5", "param5value": "Parameter 5 Value", "paramcount": "Parameter count", "scriptcontent": "script content", "fuzzyhash": "fuzzy hash", "rpcprocessid": "rpc process id", "rpcprocesslevel": "rpc process security", "rpcprocessmd5": "rpc process md5", "rpcprocesssha1": "rpc process sha1", "rpcprocess": "rpc process", "targetprocessmd5": "target process md5", "targetprocesssha1": "target process sha1"}}