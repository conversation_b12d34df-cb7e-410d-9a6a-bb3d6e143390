[service]
name = "ratp-console"
addr = "0.0.0.0:8888"
export_limit = 1000
is_private = false
context_path = "/"
access_control_allow_origin = "https://alpha-console.rongma.tech"
syncioa_interval = "5m"
# 中文 zh 英文en
language = "en"
# 查询最大限制时间（天）
query_restriction_days = 60
is_use_new_table = true

[jwt]
expired_time = 86400
prefix = "ratpconsolejwt"
sign_key = "ratpconsolejwt"

[elasticsearch_setting]
# 是否需要连接es true:需要 false:不需要
switch = true

[grpc_info]
switch = true

# 进程统计相关配置
[process_statistics]
# 上下文的过期时间 单位 秒
context_time = 30
# 是否开启redis 缓存
cache_switch = true
# reids 缓存时长
redis_cache_time = "10m"

# 开启协程的数量
[set_goroutine_chan]
incident_time_line_num = 5
instruction_concurrency_num = 5


[file_level]
isolate_file_level = 15
ioc_prevention_level = 75

[settings]
show_headers = false
show_request = true
show_response = false
host_offline_timeout = '10m'
host_offline_check_interval = '10s'
events_mapping_use_network = true
# 日志搜索告警查询提前时间
events_alarm_query_advance_time = '10s'
run_task = false
# 告警详情log里面是否展示@raw字段 new
alarm_show_raw = false
#不需要运行的任务
not_run_tasks = ["sync_ioa_data"]
process_before_time_interval = '1d'
process_after_time_interval = '1d'
# 是否开启任务锁，console-api服务如果启动多个则需要开启锁 false 不开启 true 开启
task_redis_lock_switch = false
ts_download_process_json_prefix = "/edr"
ts_pure_download_process_json_prefix = ""
ty_download_process_json_prefix = ""

[host_risk_level]
#低风险系数 每个低风险多少分
low_coefficient = 1
# 中风险系数 每个中风险多少分
medium_coefficient = 100
# 高风险系数 每个高风险多少分
high_coefficient = 1000
# 低风险分值 0 ~ max_low_score
max_low_score = 100
# 中风险分值 min_medium_score ~ max_medium_score
min_medium_score = 101
max_medium_score = 1000
# 高风险分值 大于等于 min_high_score 都是高风险
min_high_score = 1001

[process_tree]
#当节点下，取100条进程，多余不显示
max_process_num = 100
# 进程数深度
max_process_depth = 3
# 小于此数量查询下一级， 超过只查询一级
child_node_num = 5
# 查询时间往前多久
before_time_interval = "15d"

[dashboard]
event_type_num = 26
event_num = 42
operation_num = 171
t_rule_num = 2066
extort_detection_t_id=["T1486"]
phishing_detection_tid=["T1566"]

#新增gml ai配置
[ai_config]
require_url = 'https://open.bigmodel.cn/api/paas/v4/chat/completions'
api_key = '64619e6980e730473764b1eaba0819e7.NMKev8JkTAFI9C38'
exp_seconds = 3600
cache_minute = 1000
temperature = 0.01

# 新增gpt_message_headers配置
[gpt_message_headers]
assistant_message_zh = '从现在开始你是一个网络安全专家负责编写EDR的木马病毒威胁事件报告,我会分三段给你内容,你需要每一段都进行一次总结,请你严格按照要求编写总结并且分析并且给出结论，在此之前,你需要学习一些知识:#当然，为了创作一个吸引人的slogan，请告诉我一些关于您产品的信息。#1. explorer.exe是windows资源管理器,是系统进程,主要是用户启动的进程.如果你看到它存在,大概率是用户手动执行了不该执行的东西,严格按照以下要求执行。#2.请输出中文,必须输出中文,不要说英语。#3.请注意不要输出结论,因为要把你的内容合并到更大的报告里面。#4.不要说\"我\"、\"我们 #5.不要说\"建议\" #6.严格按照报告输出,请注意你在写报告的一部分而不是对话。#7.不要写总结,因为这是一大段中的一部分。#8.请用\"该样本\"称呼对方。#9.不要写报告头,也不要写根据XXX分析后开头,不要写,因为只是让你填充一部分内容,而不是整个报告,请只输出一段文字,不用带标题以及总结!请不要输出\"需要进一步\"等内容。'
assistant_message_en = 'From now on, you are a network security expert responsible for writing a Trojan threat report for EDR. I will divide the content into three paragraphs for you. You need to summarize each paragraph, and strictly follow the requirements to write a summary, analyze and draw conclusions. Before that, you need to learn some knowledge:# Of course, to create an attractive slogan, please tell me some information about your product# 1. Explorerexe is a Windows Explorer, a system process that is primarily initiated by users. If you see it, it is likely that the user manually executed something that should not have been executed, strictly following the following requirements# 2. Please output in English, it is mandatory to output in English and not in Chinese# 3. Please be careful not to output conclusions as you need to merge your content into a larger report# 4. Do not say "me" or "we # 5. Do not say" suggestions "# 6. Strictly follow the report output, please note that you are writing a part of the report instead of a conversation. # 7. Do not write a summary because it is a part of a large paragraph. # 8. Please refer to the other party as" the sample ". #9. Do not write the report header, nor do you write the beginning based on XXX analysis, because it only requires you to fill in a part of the content, not the entire report. Please only output one paragraph of text without a title or summary! Please do not output content such as "need further action".'
overview_of_attack_review_zh = "我将输入本次事件的ATT&CK矩阵与IOA告警情况,你需要输出 基于att&ck框架的分析报告总结, 对攻击方式进行复盘总结，请输出中文："
overview_of_attack_review_en = "I will input the ATT&CK matrix and IOA alarm situation for this event. You need to output an analysis report summary based on the ATT&CK framework. The specific content is to guess what type of threat it may be and what the purpose of the threat is. Output the analysis process as follows, please output in English:"
conclusion_of_safety_gtp_zh = "我将输入本次事件的ATT&CK矩阵与IOA告警情况,你需要输出基于att&ck框架的分析报告总结,具体内容是猜测可能是什么类型的威胁,该威胁的目的是什么.用中文输出 分析过程,文内容如下："
conclusion_of_safety_gtp_en = "I will input the ATT&CK matrix and IOA alarm situation for this event. You need to output an analysis report summary based on the ATT&CK framework. The specific content is to guess what type of threat it may be and what its purpose is. Output the analysis process in English, and the content is as follows:"
process_chain_gpt_zh = "我希望你输出基于进程链的分析报告,威胁的来源是什么,以及怎么执行的,还原整个攻击事件:"
process_chain_gpt_en = "I hope you can output an analysis report based on the process chain, including the sources of threats and how they were executed, to restore the entire attack event:"
network_gpt_zh = "我希望你输出基于网络攻击的分析报告,网络攻击的来源是什么:"
network_gpt_en = "I hope you can provide an analysis report based on cyber attacks, including the sources of cyber attacks"

[security_score]
virus_ioa = [
    '1691346508445650944',
    '1691356304620130304',
    '1691356816987918336',
    '1691357261621891072',
    '1691369452634902528',
    '1692092997501456384',
    '1613480299188981760',
    '1620064993003180032',
    '1627997556380274688',
    '1633027994266112000',
    '1646409930233614336',
    '1646410780125433856',
    '1651072751605125120',
    '1651179338365669376',
    '1666368061575270400',
    '1666378436022636544',
    '1671353205818134528',
    '1671356583780552704',
    '1671399399239979008',
    '1671403936927453184',
    '1671412044949098496',
    '1671415044966977536',
    '1671422836683575296',
    '1672881665287917568',
    '1672884579419033600',
    '1672886073887297536',
    '1672891043978153984',
    '1672895173408854016',
    '1672900544974295040',
    '1681967822088966144',
    '1686587979021160448',
    '1686660798388113408',
    '1686672467935891456',
    '1691349512980795392',
    '1692034128880340992',
    '1692104177217441792',
    '1693541510005395456',
    '1696014774946500608',
]
coefficient = 10
virus_score_a = 80
virus_score_b = 100

[host_group_setting]
group_num_limit =1000 # 限制orgname最大的主机组个数
import_num_limit=1000 # 限制主机组部分的导入数量
strategy_num_limit =100 #限制 策略的数量

[ai_data]
callback_url ="https://alpha-saas-openapi.rongma.tech" #这个配置只是在私有化环境处理 具体的请求地址 可以根据环境配置 这个是示例


[actlogger]
debug = true
actlog_index_name = "alpha-console-log"

[minio_corpus]
switch = true

[awss3_corpus]
switch = true