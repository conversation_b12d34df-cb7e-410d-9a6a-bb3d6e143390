[service]
name = "rm-center-alarm"
addr = "0.0.0.0:8084"
mode = "debug"
context_path = "/"
export_limit  = 2

[elasticsearch]
addr = ['http://elasticsearch-alpha.outside.rongma.tech:9200']
username = "elastic"
password = "elastic"
scroll_time = "1m"
beta_events_index = "ratp-events-*"

[mongodb_console]
addr = 'mongodb-alpha-saas.outside.rongma.tech:27017'
database = "alpha-saas-console"
username = "console-alpha-saas"
password = "000000"
options = "?directConnection=true"

[redis]
addr = ['192.168.111.195:36379']
password = ''
db = 0

[jwt]
expired_time = 86400
prefix = "admincenter"
sign_key = "admincenter"

[settings]
show_headers = false
show_request = true
show_response = false

[process_tree]
#当节点下，取100条进程，多余不显示
max_process_num = 50
# 进程数深度
max_process_depth = 5
# 小于此数量查询两级， 超过只查询一级
child_node_num = 5
# 查询时间往前多久
before_time_interval = "15d"
