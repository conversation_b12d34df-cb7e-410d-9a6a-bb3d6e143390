[service]
name = "rm-center"
addr = "0.0.0.0:8001"
mode = "debug"
context_path = "/"

[redis]
addr = ['**************:36379']
password = ''
db = 0

[jwt]
expired_time = 7200
refresh_interval = 5400
sign_key = ""

[mongodb]
addr = '***************:27017'
database = 'upgrade'
username = "upgrade-admin"
password = "000000"

# [elasticsearch]
# addr = ['http://***************:9200']
# username = ""
# password = ""

[kibana]
addr = "http://***************:5601"
username = ""
password = ""

[settings]
show_headers = false
show_request = true
show_response = false
host_offline_timeout = '10m'
host_offline_check_interval = '10s'