[service]
name = "center-dispatch-task"
mode = "debug"

[log]
type = 'json'
dir = 'log'
level = 'info'
stdout = true

[kafka_reader]
addr = ['192.168.111.122:9092','192.168.111.122:9093','192.168.111.122:9094']
username = ""
password = ""
topic = 'center-dispatch-task'
group = 'dispatch-task-reader'

[redis]
addr = ['192.168.111.122:6379']
password = ''
db = 0

[mysql]
addr = '192.168.111.195:30336'
database = 'passport'
username = "root"
password = "123"

[mongodb]
addr = '192.168.111.195:32717'
database = 'admin_center'
username = "admin_center-admin"
password = "000000"
direct = true

[mongodb_console]
addr = '192.168.111.195:32717'
database = 'console'
username = "console-admin"
password = "000000"
direct = true

[minio]
addr = '192.168.111.185:39000'
bucket = 'dispatch-task-files'
access_key = 'BOE7OIKX386H8VI066NT'
secret_key = 's4JhD0jyxjeOxe5L6GK+KcdjVgG2A3ZuceYZgekA'
download_url = "http://alpha-oss.rongma.tech/dispatch-task-files/"

