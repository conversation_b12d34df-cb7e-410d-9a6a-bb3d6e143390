[service]
name = "admin-center-license"
addr = "0.0.0.0:8008"
mode = "debug"
context_path = "/"

[settings]
show_headers = false
show_request = false
show_response = false

[redis]
addr = ['192.168.111.122:6379']
password = ''
db = 0

[jwt]
expired_time = 86400
prefix = "admincenter"
sign_key = "admincenter"

[mongodb]
addr = '192.168.111.195:32720'
database = 'admin_center'
username = "root"
password = "root"

[mongodb_console]
addr = '192.168.111.195:32720'
database = 'console'
username = "root"
password = "root"

[mysql]
addr = '192.168.111.195:30336'
database = 'passport'
username = "root"
password = "123"

[kafka_writer]
addr = ['kafka.default.svc.cluster.local:9092']
username = ""
password = ""
topic = 'center-dispatch-task'
group = ''
msg_chan_size = 0

[private_keys]
rsa_2048 = 'certs/rsa2048.prvkey'
rsa_4096 = 'certs/rsa4096.prvkey'