[service]
name = "rm-admin-center-ruletrial"
addr = "0.0.0.0:8007"
mode = "debug"
context_path = "/"

[redis]
switch = "on"
addr = ['***************:6379']
password = ''
db = 0

[jwt]
expired_time = 7200
refresh_interval = 5400
sign_key = ""

[mongodb]
switch = "on"
addr = '***************:32717'
database = 'trial_rules'
username = "trialrules-admin"
password = "000000"

[mongodb_engine]
switch = "on"
addr = '***************:32717'
database = 'engines'
username = "engines-admin"
password = "000000"

[settings]
show_headers = false
show_request = false
show_response = false
host_offline_timeout = '10m'
host_offline_check_interval = '10s'

[nats]
switch = "on"
addr = ['nats://***************:4222', 'nats://***************:4223', 'nats://***************:4224']
username = ''
password = ''