[service]
name = 'rmedr_upgrade_admin'
addr = '0.0.0.0:16002'
debug = true
encrypt = true
file_bucket = 'sample-file'
shard_bucket = 'sample-file-shards'
shard_size = 131072

[mongodb]
addr = '192.168.111.195:32720'
database = "upgrade"
username = "root"
password = "root"

[nats]
addr = ['nats://192.168.111.122:4222', 'nats://192.168.111.122:4223', 'nats://192.168.111.122:4224']
username = ''
password = ''

[private_keys]
rsa_2048 = 'certs/rsa2048.prvkey'
rsa_4096 = 'certs/rsa4096.prvkey'

[minio]
addr = 'minio.default.svc.cluster.local:9000'
access_key = 'BOE7OIKX386H8VI066NT'
secret_key = 's4JhD0jyxjeOxe5L6GK+KcdjVgG2A3ZuceYZgekA'

[redis]
addr = ['192.168.111.185:36379']
password = ''
db = 0

[jwt]
expired_time = 86400
prefix = "admincenter"
sign_key = "admincenter"

[kafka_writer]
# addr = ['192.168.111.194:39192','192.168.111.194:39193','192.168.111.194:39194']
# addr = ['192.168.111.185:39092','192.168.111.185:39093','192.168.111.185:39094']
addr = ['192.168.111.195:31090','192.168.111.195:31091','192.168.111.195:31092']
username = ""
password = ""
topic = 'events-log-checkout'
group = 'console-log-alerts-reader'
