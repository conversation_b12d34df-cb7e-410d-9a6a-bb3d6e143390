[service]
name = "admin-center-attackrules-api"
addr = "0.0.0.0:8005"
mode = "debug"
context_path = "/"
type = "Attack"
engine_id = "1611964804107341824"

[redis]
addr = ['192.168.111.185:36379']
password = ''
db = 0

[jwt]
expired_time = 7200
refresh_interval = 5400
sign_key = ""

[mysql]
addr = '192.168.111.195:30336'
database = 'attack'
username = "root"
password = "123"

[mongodb]
addr = '192.168.111.195:32720'
database = 'engines'
username = "root"
password = "root"

[mongodb_dict]
addr = '192.168.111.195:32720'
database = 'qafileinfo'
username = "root"
password = "root"

[mongodb_checkrules]
addr = '192.168.111.195:32720'
database = 'check_rules'
username = "root"
password = "root"

[settings]
show_headers = false
show_request = false
show_response = false

[nats]
addr = ['nats://192.168.111.122:4222', 'nats://192.168.111.122:4223', 'nats://192.168.111.122:4224']
username = ''
password = ''

[elasticsearch]
addr = ['http://192.168.111.195:39201']
username = "elastic"
password = "elastic"
index = "ratp-events-*"
