[service]
name = "admin-center-srms"
addr = "0.0.0.0:16001"
mode = "debug"
context_path = "/"
file_bucket = 'sample-file'
shard_bucket = 'sample-file-shards'

[settings]
show_headers = false
show_request = false
show_response = false

[jwt]
expired_time = 7200
refresh_interval = 5400
sign_key = ""

[mongodb]
addr = '192.168.111.185:32717'
database = "srms"
username = "srms-admin"
password = "000000"
direct = true

[minio]
addr = '192.168.111.185:39000'
bucket = ''
access_key = 'BOE7OIKX386H8VI066NT'
secret_key = 's4JhD0jyxjeOxe5L6GK+KcdjVgG2A3ZuceYZgekA'

[redis]
switch = "on"
addr = ['192.168.111.122:6379']
password = ''
db = 0

[jwt]
expired_time = 86400
prefix = "admincenter"
sign_key = "admincenter"