[service]
name = "rm-admin-center-client-group"
addr = "0.0.0.0:8006"
mode = "debug"
bucket = "client-group"
downloadurl = "https://alpha-oss.rongma.tech/client-group/"
context_path = "/cloud/client-group/api"

[redis]
addr = ['***************:36379']
password = ''
db = 0

[jwt]
expired_time = 86400
prefix = "admincenter"
sign_key = "admincenter"

[mongodb]
addr = '***************:32720'
database = "client_group"
username = "root"
password = "root"

[settings]
show_headers = false
show_request = false
show_response = false
host_offline_timeout = '10m'
host_offline_check_interval = '10s'

[minio]
addr = 'minio.default.svc.cluster.local:9000'
access_key = 'BOE7OIKX386H8VI066NT'
secret_key = 's4JhD0jyxjeOxe5L6GK+KcdjVgG2A3ZuceYZgekA'