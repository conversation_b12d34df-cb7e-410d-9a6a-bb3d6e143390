[service]
name = "rm-admin-center-engine"
addr = "0.0.0.0:8003"
mode = "debug"
context_path = "/"

[redis]
switch = "on"
addr = ['***************:6379']
password = ''
db = 0

[jwt]
expired_time = 7200
refresh_interval = 5400
sign_key = ""

[mongodb]
switch = "on"
addr = '***************:32717'
database = 'engines'
username = "engines-admin"
password = "000000"
options = "?directConnection=true"

[mongodb_adc]
switch = "on"
addr = '***************:32717'
database = 'admin_center'
username = "admin_center-admin"
password = "000000"
options = "?directConnection=true"
direct = true

[kafka]
switch = "on"
addr = ['***************:9092','***************:9093','***************:9094']
username = ""
password = ""
topic = 'center-dispatch-task'
group = ''

[minio]
switch = "on"
addr = '***************:39000'
access_key = 'BOE7OIKX386H8VI066NT'
secret_key = 's4JhD0jyxjeOxe5L6GK+KcdjVgG2A3ZuceYZgekA'
bucket = 'dispatch-task-files'
download_url = "http://alpha-oss.rongma.tech/dispatch-task-files/"

[settings]
show_headers = false
show_request = true
show_response = false
host_offline_timeout = '10m'
host_offline_check_interval = '10s'

[nats]
switch = "on"
addr = ['nats://***************:4222', 'nats://***************:4223', 'nats://***************:4224']
username = ''
password = ''

