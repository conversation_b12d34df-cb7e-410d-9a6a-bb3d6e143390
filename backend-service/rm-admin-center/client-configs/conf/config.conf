[service]
name = "client-configs"
addr = "0.0.0.0:5003"
mode = "debug"
context_path = "/cloud/client-configs/api"

[mongodb]
addr = 'mongodb.outside.rongma.tech:27017'
database = "cloud-info"
username = "info-cloud"
password = "000000"
direct = true

[settings]
show_headers = false
show_request = false
show_response = false

[redis]
addr = ['192.168.111.195:36379']
password = ''
db = 0

[jwt]
expired_time = 86400
prefix = "admincenter"
sign_key = "admincenter"

[trantor_jarvis_grpc]
addr = "127.0.0.1:8894"

[logging]
level = "error"
