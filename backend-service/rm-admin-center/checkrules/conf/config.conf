[service]
name = "rm-admin-center-checkrules"
addr = "0.0.0.0:8004"
mode = "debug"
context_path = "/"

[redis]
addr = ['***************:36379']
password = ''
db = 0

[jwt]
expired_time = 7200
refresh_interval = 5400
sign_key = ""

[mongodb]
addr = '***************:32720'
database = 'check_rules'
username = "root"
password = "root"

[mongodb_engine]
addr = '***************:32720'
database = 'engines'
username = "root"
password = "root"

[settings]
show_headers = false
show_request = false
show_response = false
host_offline_timeout = '10m'
host_offline_check_interval = '10s'

[kafka_writer]
addr = ['**************:9092','**************:9093','**************:9094']
username = ""
password = ""
topic = 'rmedr-rule-filters'
group = ''
msg_chan_size = 0
