[service]
name = "admin-center-proxy"
addr = "0.0.0.0:8877"
mode = "test"
context_path = "/"

[redis]
connection_mode = 0
addr = ['192.168.111.185:36379']
# addr = ['192.168.111.195:36379']
password = ''
db = 0

[mysql]
# addr = "192.168.111.195:30336"
# database = "passport"
# username = "root"
# password = "123"
# charset = "utf8mb4"

addr = "192.168.111.185:30336"
database = "passport"
password = "d9%MW3TlN"
username = "root"
charset = "utf8mb4"

[settings]
show_headers = false
show_request = true
show_response = false

[jwt]
expired_time = 86400
prefix = "admincenter"
sign_key = "admincenter"
