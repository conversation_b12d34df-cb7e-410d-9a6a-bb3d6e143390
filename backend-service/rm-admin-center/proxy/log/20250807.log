{"time":"2025-08-07 16:26:50","level":"ERROR+4","msg":"load config failed, open conf/config.conf: The system cannot find the path specified."}
{"time":"2025-08-07 16:33:08","level":"ERROR+4","msg":"load config failed, While parsing config: toml: invalid character at start of key: ;"}
{"time":"2025-08-07 16:33:36","level":"ERROR+4","msg":"load config failed, While parsing config: toml: invalid character at start of key: ;"}
{"time":"2025-08-07 16:35:16","level":"INFO","msg":"center api proxy service is running on 0.0.0.0:8877..."}
{"time":"2025-08-07 16:35:26","level":"INFO","msg":"stopping center api proxy service..."}
{"time":"2025-08-07 16:35:26","level":"INFO","msg":"center api proxy stopped."}
{"time":"2025-08-07 16:36:33","level":"INFO","msg":"center api proxy service is running on 0.0.0.0:8877..."}
{"time":"2025-08-07 16:36:39","level":"INFO","msg":"stopping center api proxy service..."}
{"time":"2025-08-07 16:36:39","level":"INFO","msg":"center api proxy stopped."}
