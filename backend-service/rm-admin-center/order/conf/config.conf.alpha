[service]
name = "order"
addr = "0.0.0.0:8083"
mode = "debug"
context_path = "/"
aes_key="TZVvJ+ZQGIBUCgAmk+bEtkHye0WizYsO"
invoice_bucket = "alpha-invoice"
# 前端机ID
dcs_id = 1

[elasticsearch]
addr = ['http://elasticsearch-alpha.outside.rongma.tech:9200']
username = "elastic"
password = "elastic"
scroll_time = "1m"

[mongodb_portal]
addr = 'mongodb-alpha.outside.rongma.tech:27017'
direct = true
database = "alpha-portal"
username = "portal-alpha"
password = "000000"

[mysql]
addr = "192.168.111.185:30336"
password = "d9%MW3TlN"
database = "passport"
username = "root"
charset = "utf8mb4"

[redis]
addr = ['192.168.111.185:36379']
password = ''
db = 0

[jwt]
expired_time = 86400
prefix = "admincenter"
sign_key = "admincenter"

[settings]
show_headers = false
show_request = true
show_response = false

[process_tree]
#当节点下，取100条进程，多余不显示
max_process_num = 50
# 进程数深度
max_process_depth = 5
# 小于此数量查询两级， 超过只查询一级
child_node_num = 5
# 查询时间往前多久
before_time_interval = "15d"

[minio]
addr = 'minio-alpha.outside.rongma.tech:9000'
access_key = 'BOE7OIKX386H8VI066NT'
secret_key = 's4JhD0jyxjeOxe5L6GK+KcdjVgG2A3ZuceYZgekA'

[email]
port = 465
host = "smtpdm.aliyun.com"
user = "<EMAIL>"
password = "Gz5SPWbsatH3"
header_name = "戎码翼龙主机威胁检测防御系统"

[alipay]
#支付宝appid
app_id      = "2021003194665165"
# 支付宝私钥
private_key = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCBS9ceoJRrGHE7ryLKTsesGLkNLzwB7U2h1R+RkbIFFDU2/P+ANQivr3mXilyHTFQL/KfKh9cISaqjKF8/Qn72it7w4y45W4HlTrMVPf0bLB7ZW7gpTzg8BgHIzrEBiDTB7OYGoL2vTj8s2lr8OPKGUAVzOrAodbyIqpt0lB943YOqH+o3sNE7rtGv29wCo3JY1sxkRdeQ4MykmUI/6U20AeXEhK/6hmXET1lab3SJVvk7W4PZc6JuKRKrzUO0utJRlCphInCXxExx/E+QnE8mKOq6HrRzl0KIy0xcbFeAlEuWInRx+bEZRIHCHIP7gDEx9aQYVHNQv7zKsCEZW6EbAgMBAAECggEAUo0MfRaDjFkfur6YFNj1rVBfniZhRs2vOjsrm7KNGacMTt57duzlbd44YOPLLesy4jBfJPVT+oumMePN5ELdcyW5L82srpxZNAHqXw6j8lxaGebwjxdwTQp8yywEYXkITiSlvOeWFD5+IXIMk2r8lXqz8B0xabO6OvvKo0ULTwwbugmQX+yLDHNI1ev3/d8KoECAb+EoPAdCLYUdDuA++ALH3VZWspXYSGQ0+hFGqnW/mxU+6ZRJaqSS/Phe5cd5B3OoeiigM+Ze2oFNRPYdS4jswC0oFJk8vUJwcPtsKe3v1EaU4wz1rra1ZNnMq0nr5tSuPLamkULcKm+inZlpcQKBgQDXAZEWZmNyg3si/DPR95/iObrEogitKCQ6xljkPL0IatoVRIUmTyiibe7AWOMZ0q7ZqI+JD3UIkPSwa3FEWYK/zLSEQdacagSeXrkDvx9SoEHxX5QWgf+3KXhpmLOomO8hUSMdAyTXHem2BL+KQxIrhnPYknh+LoMyWfWtuBNxYwKBgQCZ8sfxjHGEFTakT915rl4BAUO7jJwt3u94oE9XQH+9rrSWNkk2OZjCwTcBKLb/yz3bhdI57EE1eoUEP9ySAadeNnWyKe9Z9c0fezPUukxaHSistiYCXJoTQ8XYzTrTpFbZQ7Q477j6oUzCoFy3H9DqAWCRMMA6g3zA+0F4jfM66QKBgQDAhTOgRiBIOrk483GHUl7E2AKH7bstKaN/SXnxLP3rGpqarpc3XWw6PYuqii0YQ1Gzrj6QcNnPyNSBYEO0+x2blrYRM07zEe9+y7yJQUrP1pYVhwr4OIF8RdL/Yu+ESSTexWNWqCkFwlAyXwAP0szjUmpLA6SHACkUHf9iBVDLlwKBgFl6FaIcGeJGHiM0VCqkIPj+jiqU2DWFDLSUoGi7EUPjJgzkcnr00QfpGxouWuajb0mHJyAJZAE8O9GzeHTYRznn91JdM0ObLOSuS/rJaRU7O2fe2oqm3dpymqye5CEctGnFAxvQoTfyxlWctSaudFN+B217405fHARiRLRX9NX5AoGANwNCdXg7lUlFjhL5xJhbaD3AKPaLbyl2BwuFAQLjUvnzrs0LpmRtzDr2Uk2aZimjKT5FMPZswfYILF/xQXea8Sw5gWQ2p3PwC37cwmUVeXXfwYLJzTc+fQKSaO1vcFjEYpi5Q7JqqR6SstJDJW58axEYzsbiAfD76Plx6LQgyo0="
# 公钥证书
cert_public_key = "cert/alipay/appCertPublicKey.crt"
# 支付宝根证书
alipay_root_cert = "cert/alipay/alipayRootCert.crt"
# 支付宝公钥证书
alipay_cert_public_key_RSA2 = "cert/alipay/alipayCertPublicKey_RSA2.crt"