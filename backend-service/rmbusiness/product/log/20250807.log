{"time":"2025-08-07 16:06:40","level":"ERROR+4","msg":"connect to mongodb failed, error parsing uri: must have at least 1 host"}
{"time":"2025-08-07 16:06:40","level":"ERROR+4","msg":"invalid private key"}
{"time":"2025-08-07 16:06:40","level":"INFO","msg":"order backend service starting..."}
{"time":"2025-08-07 16:07:50","level":"INFO","msg":"stopping order backend service..."}
{"time":"2025-08-07 16:07:54","level":"INFO","msg":"product backend service starting..."}
{"time":"2025-08-07 16:07:57","level":"INFO","msg":"stopping product backend service..."}
{"time":"2025-08-07 16:07:57","level":"INFO","msg":"product backend service stopped."}
{"time":"2025-08-07 16:08:03","level":"INFO","msg":"product backend service starting..."}
{"time":"2025-08-07 16:08:06","level":"INFO","msg":"stopping product backend service..."}
{"time":"2025-08-07 16:08:06","level":"INFO","msg":"product backend service stopped."}
{"time":"2025-08-07 16:08:08","level":"INFO","msg":"product backend service starting..."}
{"time":"2025-08-07 16:08:10","level":"INFO","msg":"stopping product backend service..."}
{"time":"2025-08-07 16:08:10","level":"INFO","msg":"product backend service stopped."}
{"time":"2025-08-07 16:08:15","level":"INFO","msg":"product backend service starting..."}
{"time":"2025-08-07 16:09:11","level":"INFO","msg":"stopping product backend service..."}
{"time":"2025-08-07 16:09:11","level":"INFO","msg":"product backend service stopped."}
