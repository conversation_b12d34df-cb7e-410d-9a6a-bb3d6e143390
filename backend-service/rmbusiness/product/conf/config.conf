[service]
name = "product"
addr = '0.0.0.0:8081'
debug = true
bucket = "product"
jwt_key = "ajwa430AFLG{}<K"
aes_key = "TZVvJ+ZQGIBUCgAmk+bEtkHye0WizYsO"
downloadurl = "https://alpha-oss.rongma.tech/product/"
access_control_allow_origin = "https://alpha-saas-passport.rongma.tech"
# 语言
language = 'en'
# 海外版 origin
overseas_origin = "passport.rongma.cn"

[redis]
addr = '192.168.111.185:36379'
password = ''
db = 0
# 如果addr配置项是集群连接则是false,单节点连接则是true new
is_single = true

[mysql]
addr = "192.168.111.185:30336"
password = "d9%MW3TlN"
database = "passport"
username = "root"
charset = "utf8mb4"

[mongodb_portal]
addr = 'mongodb-alpha.outside.rongma.tech:27017'
direct = true
database = "alpha-portal"
username = "portal-alpha"
password = "000000"
options = "?directConnection=true"

[minio]
addr = 'minio-alpha.outside.rongma.tech:9000'
access_key = 'BOE7OIKX386H8VI066NT'
secret_key = 's4JhD0jyxjeOxe5L6GK+KcdjVgG2A3ZuceYZgekA'