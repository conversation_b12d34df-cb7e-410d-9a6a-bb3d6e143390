[service]
name = 'passport_admin'
addr = '0.0.0.0:8086'

debug = true
aes_key = "TZVvJ+ZQGIBUCgAmk+bEtkHye0WizYsO"
downloadurl = "https://alpha-saas-oss.rongma.tech/passport/"
access_control_allow_origin = "*"
# 注册后 licence 默认过期天数
expire_day = 15
# 注册后 licence 默认允许的客户端数量
client_count = 5

[redis]
addr = '192.168.111.195:36379'
password = ''
db = 0
# 如果addr配置项是集群连接则是false,单节点连接则是true new
is_single = true

[mysql]
addr = "192.168.111.195:30336"
database = "passport"
username = "root"
password = "123"
charset = "utf8mb4"

[minio]
addr = '192.168.111.195:39000'
access_key = 'BOE7OIKX386H8VI066NT'
secret_key = 's4JhD0jyxjeOxe5L6GK+KcdjVgG2A3ZuceYZgekA'


[mongodb_portal]
addr = '192.168.111.195:32720'
database = "portal"
username = "portal-admin"
password = "000000"
direct = true