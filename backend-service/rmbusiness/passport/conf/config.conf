[service]
name = 'passport'
addr = '0.0.0.0:8087'
debug = true
bucket = "passport"
jwt_key = "ajwa430AFLG{}<K"
aes_key = "TZVvJ+ZQGIBUCgAmk+bEtkHye0WizYsO"
downloadurl = "https://alpha-oss.rongma.tech/passport/"
access_control_allow_origin = ["*"]
# 邀请链接
invitation_url = "http://192.168.111.114:3000/invite?callback=%s"
# 团队最大成员数量
org_max_members = 10
# 是否允许测试手机号登录注册（11 或 12开头的）,生产环境不添加
is_allow_test_phone = true
invitation_sms_tpl = "1814852"
# new 前端机id
dcs_id = 1

#
[website]
app_id = 1

#
[console]
app_id = 48
jwt_prefix = "ratpconsolejwt_test"

# new
[sensor]
app_id = 49
jwt_prefix = "ratpconsolejwt_test"

[redis]
addr = '192.168.111.185:36379'
password = ''
db = 0
# 如果addr配置项是集群连接则是false,单节点连接则是true new
is_single = true

[mysql]
addr = "192.168.111.185:30336"
database = "passport"
username = "root"
# password = "123"
password = "d9%MW3TlN"
charset = "utf8mb4"

[mongodb_portal]
addr = 'mongodb-alpha.outside.rongma.tech:27017'
database = "alpha-portal"
username = "portal-alpha"
password = "000000"
options = "?directConnection=true"

[minio]
addr = 'minio-alpha-saas.outside.rongma.tech:9000'
access_key = 'BOE7OIKX386H8VI066NT'
secret_key = 's4JhD0jyxjeOxe5L6GK+KcdjVgG2A3ZuceYZgekA'

[sms]
secret_id = 'AKIDqaCcqpaBYfYAZjfGvPjrkPr6B0Zv53nK'
secret_key = 'NVSMEqCI8h9QYWFjxwf7KG84qPkgMnzu'
sms_sdk_appid = '1400789505'
captcha_appid = *********
captcha_app_secret_key = 'MhZw9KSblVv6JVtDubE0ImQGE'
sign_name = '戎码科技'
# 同一个手机号验证码两次发送时间间隔（秒）
verification_code_interval = 120
# 每个手机号验证码有效时间（分钟）
verification_code_valid_time = 5
# 【不替换生产的】同一个手机号验证码每天允许的最大发送次数，0点更新
verification_code_times_day_for_phone = 30
# 【不替换生产的】同一个IP验证码每天允许的最大发送次数，0点更新
verification_code_times_day_for_ip = 100