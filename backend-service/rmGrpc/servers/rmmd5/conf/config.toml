[service]
name = 'grpc-rmmd5'
http = '0.0.0.0:8880'
grpc = "0.0.0.0:8889"

[redis_disposition]
# 高风险等级文件区间
high_risk_file_min = 30
high_risk_file_max = 60
# redis 高风险等级文件缓存时间（秒）
redis_expiration_heigh = 3600
# redis 低风险等级文件缓存时间（秒）
redis_expiration_low = 2592000
# 没设置自定义等级、奇安信也没找到缓存时间（秒）
not_fount_redis_expiration = 3600
# redis 严重风险缓存时间（秒）
redis_url_expiration_critical = 1800
# redis 高风险缓存时间（秒）
redis_url_expiration_heigh = 3600
# redis 中风险缓存时间（秒）
redis_url_expiration_medium = 7200
# redis 低风险缓存时间（秒）
redis_url_expiration_low = 18000
# 奇安信也没找到缓存时间（秒）
redis_url_expiration_not = 3600

[grpc_qianxin_args]
app_key = "3bd829111c9e40da98d4c7e36512e30yuncha3"
app_secret = "83208404-a032-499d-80d7-1427fc9c9893"
product = "rmedr_linux"
combo = "level_detection"
url_combo = "url_detection"
