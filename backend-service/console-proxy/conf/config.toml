[service]
name = "ratp-console-proxy"
addr = "0.0.0.0:8888"
mode = "debug"
is_private = false
context_path = "/"
access_control_allow_origin = "https://yilong.rongma.com"
Lang = "cn"
export_limit = 1000

[settings]
show_headers = false
show_request = false
show_response = false

[mongodb_console_proxy]
# direct 是否直连，一般开发阶段本地连测试服务器改成 true
# 发到 alpha alpha-saas 及以上环境，不要这项，直接删除
direct = true

[jwt]
expired_time = 86400
prefix = "ratpconsolejwt"
sign_key = "ratpconsolejwt"

[passport]
appid = 44
app_secret = "3653b9340cc682ea711560529085e87b"
passport_web_url = "https://passport.rongma.com"
passport_backend_url = "http://passport-api.passport.svc.cluster.local/rm_passport/v1"
console_web_callback_url = "https://yilong.rongma.com/primaryVerifyToken"
