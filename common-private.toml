[service]
mode = 'debug'
ccs_name = 'alpha'
# is_saas 设置是否是 saas，私有化部署，不加这个配置项
is_saas = true

############# 服务基础配置 ##################

############# MySQL 配置 ##################


############# ClickHouse 配置 ##################
[clickhouse_default]
resource_prefix = 'rongma'
addr = '192.168.111.177:9019'
username = "root"
password = "iynbSkWy"

############# Mongodb 配置 ##################
[mongodb_default]
resource_prefix = ''
addr = '192.168.111.177:27017'
username = 'rmclient'
password = 'i#w3eyh!Ni$Z5&GZ'

[mongodb_console_proxy]
resource_prefix = ''
addr = '192.168.111.177:27017'
username = 'rmclient'
password = 'i#w3eyh!Ni$Z5&GZ'

[mongodb_engine]
resource_prefix = ''
addr = '192.168.111.177:27017'
username = 'rmclient'
password = 'i#w3eyh!Ni$Z5&GZ'

[mongodb_cloud_upgrade]
resource_prefix = 'cloud_'
addr = 'mongodb.outside.rongma.tech:27017'
username = "cloud"
password = "RYI5*6!odrow$CLJi"

[certs]
rsa_2048 = 'certs/rsa2048.prvkey'
rsa_4096 = 'certs/rsa4096.prvkey'
crt = "certs/client.crt"
key = "certs/client.key"
ca = "certs/ca.crt"
qax_grpc = 'certs/qianxin.crt'

############# 各种证书配置 ##################

############# Grpc 配置 ##################

[grpc_info]
# 是否验证证书
is_cheack_cert = true
addr = 'alpha-saas-grpc.rongma.tech:8443'

[grpc_srms]
# 是否验证证书
is_cheack_cert = true
addr = 'alpha-saas-grpc.rongma.tech:8443'

[grpc_instructions]
# 是否验证证书
is_cheack_cert = true
addr = 'alpha-saas-grpc.rongma.tech:8443'

[grpc_upgrade]
# 是否验证证书
is_cheack_cert = true
addr = 'alpha-saas-grpc.rongma.tech:8443'

[grpc_portal]
# 是否验证证书
is_cheack_cert = false
addr = '127.0.0.1:6000'

[grpc_file]
# 是否验证证书
is_cheack_cert = false
addr = '192.168.111.185:38889'

[grpc_engine_query]
# 是否验证证书
is_cheack_cert = false
addr = '192.168.111.185:38082'

[grpc_qianxin]
# 是否验证证书
is_cheack_cert = true
addr = 'styxcl.b.qianxin.com:8443'

############# Redis 配置 ##################
# connection_mode 连接方式，0 为单机模式，1 为集群模式
[redis_default]
connection_mode = 0
addr = ['192.168.111.177:6379']
password = 'rm@123#'
db = 0

[redis_engine]
connection_mode = 0
addr = ['192.168.111.177:6379']
password = 'rm@123#'
db = 1

############# Redis 配置 ##################



############# Minio 配置 ##################
# 资源前缀，桶名前缀，比如 alpha-failed-upload，那么配 alpha-
[minio_default]
resource_prefix = ''
addr = '192.168.111.177:9000'
access_key = 'BOE7OIKX386H8VI066NT'
secret_key = 's4JhD0jyxjeOxe5L6GK+KcdjVgG2A3ZuceYZgekA'
# 下载地址，不要以 / 结尾
download_url = 'https://alpha-oss.rongma.tech'

[minio_01]
# 资源前缀，桶名前缀，比如 alpha-failed-upload，那么配 alpha-
resource_prefix = ''
addr = '192.168.111.177:9000'
access_key = 'BOE7OIKX386H8VI066NT'
secret_key = 's4JhD0jyxjeOxe5L6GK+KcdjVgG2A3ZuceYZgekA'
# 下载地址，不要以 / 结尾
download_url = 'https://download.rongma.com'

############# Kafka 配置 ##################
# 资源前缀，topic 前缀，比如 alpha-preduce-event，那么配 alpha-
[kafka_default]
resource_prefix = ''
addr = ['192.168.111.177:9092']
username = 'az'
password = 'AZ2023'

[kafka_engine]
resource_prefix = ''
addr = ['192.168.111.177:9092']
username = 'az'
password = 'AZ2023'



[actlogger]
actlog_index_name = "alpha-actlog"