
<!-- @import "[TOC]" {cmd="toc" depthFrom=1 depthTo=6 orderedList=false} -->

<!-- code_chunk_output -->

- [统一配置文件说明文档](#统一配置文件说明文档)
  - [背景说明](#背景说明)
  - [特别注意](#特别注意)
  - [运维需要特别注意的](#运维需要特别注意的)
  - [云端服务代码](#云端服务代码)
  - [开发调试建议](#开发调试建议)
    - [Linux](#linux)
    - [Windows](#windows)
      - [cmd](#cmd)
      - [PowerShell](#powershell)

<!-- /code_chunk_output -->

# 统一配置文件说明文档

## 背景说明

* 压缩包 go-build 里是已经调好的，涉及到的配置修改
* 把中间件（MongoDB、MySQL、ClickHouse、Kafka、Redis、MinIO、gRPC、NATS、Certs、ElasticSearch）的连接地址、用户名、密码，放到公共配置文件里
    * 公共配置文件：common.toml
    * 前端机服务涉及到的代码仓库：
        * client_api: [GitLab](https://gitlab.rongma.tech/client_api)
            * center_api
            * client_info
            * client_instructions
            * file_dump
            * file_inform
            * log_status
            * logagent
            * portal
            * report
            * srms
            * task
            * upgrade
        * backend-service: [GitLab](https://gitlab.rongma.tech/backend-service)
            * ratp-console
            * rm-admin-center
            * rm-streamengine
            * rmgrpc

## 特别注意

* <span style="color: red;">业务配置文件，统一在各自的 conf/config.toml</span>
* <span style="color: red;">如下数据需要写死到代码，写死到代码的不用写公共配置里的前缀：</span>
    * <span style="color: red;">MongoDB 的库名（database）</span>
    * <span style="color: red;">MySQL 的库名（database）</span>
    * <span style="color: red;">ClickHouse 的库名（database）</span>
    * <span style="color: red;">Kafka 的（topic/group ... 等）</span>
    * <span style="color: red;">MinIO 的桶名（bucket）</span>
    * <span style="color: red;">ElasticSearch 的索引名（index）</span>
* <span style="color: red;">公共配置文件只准运维修改</span>
* <span style="color: red;">公共代码库里的配置结构体，支持开关项，但开关业务需要各自业务开发自行实现。参考代码：backend-service/rm-streamengine</span>
* <span style="color: red;">业务配置里的内容会替换公共配置，例如本地开发需要直连 MongoDB，参考：backend-service/console-api、client_api/portal、client_api/srms、已经各个 engine 的配置。</span>

## 运维需要特别注意的
* <span style="color: green;">业务配置文件，统一在各自的 conf/config.toml</span>
* <span style="color: green;">公共配置文件 common.toml 映射进各个服务的 conf 里，跟业务配置 config.toml 平级</span>
* <span style="color: green;">公共配置文件 common.toml 里涉及到的 certs 路径，是基于服务根目录的相对路径</span>

## 云端服务代码

> 但不涉及到配置文件调整，主要提供了一些 gRPC 接口

* cloud_api: [GitLab](https://gitlab.rongma.tech/cloud_api)
            * cloud_upgrade
            * cloud_instructions

## 开发调试建议

在本地开发时，可以把 common.toml 软链接到自己维护的各个项目里，这样公共配置改一遍，其他项目都能用了

    举例：
        Linux:
            公共配置路径：/data/go-build/common.toml
            业务 build 路径：/data/go-build/client_api/portal
        Windows:
            公共配置路径：E:\go-build\common.toml
            业务 build 路径：E:\go-build\client_api\portal

### Linux

```bash[common.toml](backend-service/console-api/conf/common.toml)
cd /data/go-build/client_api/portal/conf
ln -s /data/go-build/common.toml ./common.toml

或者

cd /data/go-build/client_api/portal/conf && ln -s /data/go-build/common.toml ./common.toml

或者

ln -s /data/go-build/common.toml /data/go-build/client_api/portal/conf/common.toml
```

### Windows

#### cmd

```bash
cd E:\go-build\client_api\portal\conf
mklink .\common.toml E:\go-build\common.toml

或者

mklink E:\go-build\client_api\portal\conf\common.toml E:\go-build\common.toml
```
#### PowerShell
```bash
cd E:\go-build\client_api\portal\conf
New-Item -ItemType SymbolicLink -Path .\common.toml -Target E:\go-build\common.toml

或者

New-Item -ItemType SymbolicLink -Path E:\go-build\client_api\portal\conf\common.toml -Target E:\go-build\common.toml
```